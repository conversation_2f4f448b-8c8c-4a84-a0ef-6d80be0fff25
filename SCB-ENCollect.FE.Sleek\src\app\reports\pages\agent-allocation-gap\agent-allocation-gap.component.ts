import {
  AfterViewInit,
  Component,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ACMP, AcmService } from 'src/app/shared';
import { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';
import { HierarchyFormFieldComponent } from 'src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component';
import { HierarchyFormDirective } from 'src/app/shared/directives/hierarchy-form.directive';
import { ReportService } from '../../reports.service';
import { Observable, of } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { UserService } from 'src/app/authentication/user.service';
import * as d3 from 'd3';

interface Datum {
  trailStatus: string;
  noOfAccounts: number;
}

@Component({
  selector: 'app-agent-allocation-gap',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BreadcrumbComponent,
    HierarchyFormDirective,
    HierarchyFormFieldComponent,
    TypeaheadModule,
  ],
  templateUrl: './agent-allocation-gap.component.html',
  styleUrl: './agent-allocation-gap.component.scss',
})
export class AgentAllocationGapComponent implements OnInit, AfterViewInit {
  private acmService = inject(AcmService);
  private reportService = inject(ReportService);
  private toastr = inject(ToastrService);
  private userService = inject(UserService);

  @ViewChild('pr') productHierarchy!: HierarchyFormDirective;
  @ViewChild('buc') bucketHierarchy!: HierarchyFormDirective;
  @ViewChild('geo') geoHierarchy!: HierarchyFormDirective;

  breadcrumbData = [
    { label: 'Reports' },
    { label: 'Allocation Reports' },
    { label: 'Agent Allocation Gap Report' },
  ];

  canDownloadReport = this.acmService.hasACMAccess([
    ACMP.CanDownloadAgentAllocationGapReport,
  ]);

  loader = {
    generateReport: false,
    downloadReport: false,
  };

  searchForm!: FormGroup;

  reportType = 'bank';
  agencyUser = false;

  staff = '';
  selectedOwnerId = '';

  bankUserList: Observable<any[]> = of([]);
  ownertypeaheadLoading = false;

  branchName = '';
  selectedBranchId = '';
  basebranches: any[] = [];
  branchnoResult = false;
  branchtypeaheadLoading = false;

  AgencyName = '';
  selectedAgencyId = '';
  agencyList: any[] = [];
  agencynoResult = false;
  agencytypeaheadLoading = false;

  userDetails: any;
  results: any = {};
  showChart = false;

  ngOnInit(): void {
    this.buildFormGroup();
    this.initializeData();
  }

  ngAfterViewInit(): void {
    this.searchForm.addControl('products', this.productHierarchy.formGroup);
    this.searchForm.addControl('buckets', this.bucketHierarchy.formGroup);
    this.searchForm.addControl('geos', this.geoHierarchy.formGroup);
    this.searchForm.updateValueAndValidity();
  }

  buildFormGroup(): void {
    this.searchForm = new FormGroup({
      allocationOwners: new FormControl([]),
      staff: new FormControl(''),
      branch: new FormControl(''),
      agency: new FormControl('')
    });
  }

  initializeData(): void {
    try {
      this.userDetails = JSON.parse(window.localStorage['currentUser'] || '{}');
    } catch {
      this.userDetails = {};
    }

    this.reportService.getBaseBranches().subscribe(
      (basebranches) => (this.basebranches = basebranches),
      (err) => this.toastr.error(err, 'Error!')
    );

    this.getAgencyList();
  }

  resetvaluesNew(): void {
    this.staff = '';
    this.selectedOwnerId = '';
    this.branchName = '';
    this.selectedBranchId = '';
    this.AgencyName = '';
    this.selectedAgencyId = '';
  }

generatePayload() {
  const fValue = this.searchForm?.value;
  const obj: any = {};

  obj.isCompanyuser = this.reportType === 'bank';

  // Hierarchies
  const productLevels = Object.entries(fValue?.products || {})
    .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)
    .map(([levelId, masterId]) => ({ levelId, masterId }));
  if (productLevels.length) obj.products = { levels: productLevels };

  const geoLevels = Object.entries(fValue?.geos || {})
    .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)
    .map(([levelId, masterId]) => ({ levelId, masterId }));
  if (geoLevels.length) obj.geos = { levels: geoLevels };

  if (fValue?.buckets?.bucket?.length) obj.buckets = fValue.buckets.bucket;

 
  if (this.selectedOwnerId) obj.allocationOwners = [this.selectedOwnerId];
  if (this.selectedBranchId) obj.userBranches = [this.selectedBranchId];
  if (this.selectedAgencyId) obj.agencies = [this.selectedAgencyId];

  return obj;
}

  getSupervisorList(term = ''): void {
    if (!term.trim()) {
      this.bankUserList = of([]);
      return;
    }
    this.bankUserList = this.reportService.getSupervisor(term);
  }

  onSelectStaff(event: any): void {
    this.staff = `${event.item.firstName} - ${event.item.agencyCode}`;
    this.selectedOwnerId = event.item.id;
    this.searchForm.get('staff')?.setValue(this.staff);
  }

  ownerNameEmpty(): void {
    const value = this.searchForm.get('staff')?.value;
    if (!value || typeof value !== 'object') {
      this.staff = '';
      this.selectedOwnerId = '';
      this.searchForm.get('staff')?.setValue('');
    }
  }


  typeaheadNoResults(event: boolean): void {
    if (event) this.toastr.info('Please enter correct supervising manager', 'Info!');
  }

  onBranchSelect(event: any): void {
    this.branchName = event.item.name;
    this.selectedBranchId = event.item.id;
  }

  branchNameEmpty(): void {
    this.branchName = '';
    this.selectedBranchId = '';
  }

  branchNoResults(event: boolean): void {
    this.branchnoResult = event;
  }

  branchChangeLoading(event: boolean): void {
    this.branchtypeaheadLoading = event;
  }

  getAgencyList(): void {
    this.reportService.getFieldTeleAgencyName().subscribe(
      (response) => {
        this.agencyList = response;
        const agencyRoles = [
          'AgencyToBackEndExternalBIAP',
          'AgencyToFrontEndExternalBIAP',
          'AgencyToFrontEndExternalFOS',
          'AgencyToFrontEndExternalTC',
        ];
        const role = this.userService.getPrimaryRole();
        if (agencyRoles.includes(role)) {
          this.agencyUser = true;
          this.reportType = 'agency';
          const abc = `${this.userDetails.agencyFirstName} ${this.userDetails.agencyLastName} ${this.userDetails.agencyCode}`;
          this.AgencyName = abc.replace('null', '');
          this.selectedAgencyId = this.userDetails.agencyId;
        } else {
          this.agencyUser = false;
        }
      },
      (err) => this.toastr.error(err, 'Error!')
    );
  }

  onAgencySelect(event: any): void {
    this.AgencyName = `${event.item.firstName}-${event.item.agencyCode}`;
    this.selectedAgencyId = event.item.id;
  }

  agencyNameEmpty(): void {
    this.AgencyName = '';
    this.selectedAgencyId = '';
  }

  agencyNoResults(event: boolean): void {
    this.agencynoResult = event;
  }

  agencyChangeLoading(event: boolean): void {
    this.agencytypeaheadLoading = event;
  }

 generateReport(): void {
  const payload = this.generatePayload();
  this.loader.generateReport = true;
  this.showChart = false;
  d3.select('svg').selectAll("*").remove();

  this.reportService.generateAgentGap(payload).subscribe({
    next: (response) => {
      this.loader.generateReport = false;
      if (response.allocatedCount == 0 && response.unAllocatedCount == 0) {
        this.toastr.info('No results found!', "Info!");
        this.showChart = false;
        return;
      }
      this.results = response;
      this.showChart = true;
      setTimeout(() => this.createChart1(), 100);
    },
    error: (err) => {
      this.loader.generateReport = false;
      this.toastr.error(err?.message || 'Something went wrong', 'Error!');
    },
  });
}


  createChart1(): void {
    const chartData = {
      title: "Agent Allocated & Un-Allocated Accounts Pie Report",
      data: [
        {
          trailStatus: "Allocated",
          noOfAccounts: this.results?.allocatedCount ?? 0,
        },
        {
          trailStatus: "Un-Allocated",
          noOfAccounts: this.results?.unAllocatedCount ?? 0,
        },
      ],
    };

    const svg = d3.select("svg");
    svg.selectAll("*").remove();

    const width = 960;
    const height = 500;
    const chartWidth = 400;
    const chartHeight = 400;
    const radius = Math.min(chartWidth, chartHeight) / 2;

    const colorScale = d3
      .scaleOrdinal<string>()
      .domain(["Allocated", "Un-Allocated"])
      .range(["#27ae60", "#9b59b6"]);

    const arc = d3.arc<any>().outerRadius(radius - 25).innerRadius(1.5);

    const pie = d3
      .pie<any>()
      .sort(null)
      .value((d) => d.noOfAccounts)
      .padAngle(0.01);

    // Add title at the top
    svg
      .append("text")
      .attr("x", width / 2)
      .attr("y", 30)
      .style("text-anchor", "middle")
      .style("font", "16px sans-serif")
      .style("font-weight", "bold")
      .text(chartData.title);

    // Position chart on the left side
    const arcGroup = svg
      .append("g")
      .attr("class", "arc-group")
      .attr("transform", `translate(${chartWidth / 2 + 50}, ${height / 2})`);

    const arcs = arcGroup
      .selectAll(".arc")
      .data(pie(chartData.data))
      .enter()
      .append("g")
      .attr("class", "arc");

    arcs
      .append("path")
      .attr("d", <any>arc)
      .attr("fill", (d) => colorScale(d.data.trailStatus))
      .on("mouseover", (event, d) => {
        d3.select("#tooltip")
          .style("display", "inline-block")
          .style("left", event.pageX + "px")
          .style("top", event.pageY + "px")
          .style("opacity", 1)
          .select("#value")
          .html(
            `<b>Status:</b> ${d.data.trailStatus}, <b>Value:</b> ${d.data.noOfAccounts}`
          );
      })
      .on("mouseout", () => {
        d3.select("#tooltip").style("display", "none");
      });

    // Position legend on the right side, vertically centered
    const legendBox = svg
      .append("g")
      .attr("class", "legend-group")
      .attr("transform", `translate(${chartWidth + 150}, ${height / 2 - (chartData.data.length * 25) / 2})`);

    const legend = legendBox
      .selectAll(".legend")
      .data(chartData.data)
      .enter()
      .append("g")
      .attr("class", "legend")
      .attr("transform", (d, i) => `translate(0, ${i * 25})`);

    legend
      .append("circle")
      .attr("r", 8)
      .attr("fill", (d) => colorScale(d.trailStatus));

    legend
      .append("text")
      .attr("x", 15)
      .attr("y", 5)
      .style("font", "14px sans-serif")
      .text((d) => `${d.trailStatus} (${d.noOfAccounts})`);
}

downloadReport() {
  const payload = this.generatePayload();
  this.reportService.downloadSecondaryAgent(payload).subscribe(
    response => {
      const fileName = response.filename;
      this.getdata(fileName);
    },
    err => {
      this.toastr.error(err, "Error!");
    }
  );
}

getdata(fileName: string) {
  this.reportService.downloadFile(fileName).subscribe(response => {
    if (response.length === 0) {
      this.toastr.warning('No results found!', "Warning!");
    } else {
      const mediaType = 'application/zip';
      const a = document.createElement("a");
      a.setAttribute('style', 'display:none;');
      document.body.appendChild(a);
      const blob = new Blob([response], { type: mediaType });
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = 'agentgap.zip';
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }
  });
}
}
