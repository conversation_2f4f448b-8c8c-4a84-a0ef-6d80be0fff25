<div class="inner-layout-container">
   <app-breadcrumb [data]="breadcrumbData"> </app-breadcrumb>
   <h2 class="title">Agency Allocation Gap Report</h2>
   <div class="enc-card">
      <div class="card-content" [formGroup]="searchForm">
         <div class="row">
            <ng-container appHierarchyForm #pr="appHierarchyForm" [group]="'Product'" [required]="true">
            @for (level of pr?.levels; let i = $index; track level.id) {
            <app-hierarchy-form-field [level]="level" [hierarchyForm]="pr" [index]="i" class="col-lg-4 col-md-6"> </app-hierarchy-form-field>
            }
            </ng-container>
            <ng-container appHierarchyForm #buc="appHierarchyForm" [group]="'Bucket'" [required]="true">
            @for (level of buc?.levels; let i = $index; track level.id) {
            <app-hierarchy-form-field [level]="level" [hierarchyForm]="buc" [index]="i" class="col-lg-4 col-md-6"> </app-hierarchy-form-field>
            }
            </ng-container>
            <ng-container appHierarchyForm #geo="appHierarchyForm" [group]="'Geo'" [required]="true">
            @for (level of geo?.levels; let i = $index; track level.id) {
            <app-hierarchy-form-field [level]="level" [hierarchyForm]="geo" [index]="i" class="col-lg-4 col-md-6"> </app-hierarchy-form-field>
            }
            </ng-container>
            <div class="form-control-group col-lg-4 col-md-6">
               <label class="form-label">Owner</label>
               @if (ownertypeaheadLoading) {
               <span>
               <img src="assets/images/loader.gif" height="20px" width="20px" />
               </span>
               }
               <input
               class="form-control"
               (blur)="ownerNameEmpty()"
               [typeaheadItemTemplate]="customItemTemplate"
               [typeaheadInputFormatter]="ownerInputFormatter"
               [typeaheadAsync]="true"
               formControlName="allocationOwners"
               (input)="getSupervisorList($event.target.value)"
               name="bankStaff"
               id="bank-staff-name"
               (typeaheadNoResults)="typeaheadNoResults($event)"
               [typeaheadScrollable]="true"
               [typeahead]="bankUserList"
               [container]="'body'"
               [typeaheadOptionsInScrollableView]="7"
               [typeaheadOptionField]="null"
               (typeaheadOnSelect)="onSelectStaff($event)"
               (typeaheadNoResults)="typeaheadNoResultsStaff($event)"
               />
               @if (noResultStaff) {
               <div class="form-error">No Results Found</div>
               }
            </div>
            <ng-template #customItemTemplate let-model="item" let-index="index">
               <div>{{ model.firstName }} - {{ model.agencyCode }}</div>
            </ng-template>
         </div>
      </div>
      <div class="card-footer">
         <button id="generate-report-button" type="button" class="btn btn-secondary mw-150px" (click)="generateReport()" [disabled]="searchForm?.invalid">
         Generate Report @if (loader.generateReport) {
         <span>
         <img src="assets/images/loader.gif" height="20px" width="20px" />
         </span>
         }
         </button>
         @if (canDownloadReport) {
         <button id="download-report-button" class="btn btn-outline-dark mw-150px ms-4" (click)="downloadReport()" [disabled]="searchForm?.invalid">
         Download Report
         </button>
         }
      </div>
   </div>
   @if (data && (data.allocatedCount || data.unAllocatedCount)) {
   <div class="enc-card mt-4">
      <div class="card-content">
         @if (showChart) {
         <div class="chart-container">
            <svg width="960" height="500" id="report-chart"></svg>
         </div>
         }
      </div>
   </div>
   }
</div>
<div id="tooltip">
   <p>
      <span id="value">100</span>
   </p>
</div>
<ng-template #customItemTemplate let-model="item" let-index="index">
   <div>{{ model.firstName }}-{{ model.agencyCode }}</div>
</ng-template>
