{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./trail-gap.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./trail-gap.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from '@angular/core';\nimport { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { ACMP, AcmService } from 'src/app/shared';\nimport { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';\nimport { HierarchyFormFieldComponent } from 'src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component';\nimport { HierarchyFormDirective } from 'src/app/shared/directives/hierarchy-form.directive';\nimport { ReportService } from '../../reports.service';\nimport { of } from 'rxjs';\nimport { ToastrService } from 'ngx-toastr';\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\nimport { UserService } from 'src/app/authentication/user.service';\nimport * as d3 from 'd3';\nlet TrailGapComponent = class TrailGapComponent {\n  constructor() {\n    this.acmService = inject(AcmService);\n    this.reportService = inject(ReportService);\n    this.toastr = inject(ToastrService);\n    this.userService = inject(UserService);\n    this.breadcrumbData = [{\n      label: 'Reports'\n    }, {\n      label: 'Allocation Reports'\n    }, {\n      label: 'Agent Allocation Gap Report'\n    }];\n    this.canDownloadReport = this.acmService.hasACMAccess([ACMP.CanDownloadAgentAllocationGapReport]);\n    this.loader = {\n      generateReport: false,\n      downloadReport: false\n    };\n    this.reportType = 'bank';\n    this.agencyUser = false;\n    this.staff = '';\n    this.selectedOwnerId = '';\n    this.bankUserList = of([]);\n    this.ownertypeaheadLoading = false;\n    this.branchName = '';\n    this.selectedBranchId = '';\n    this.basebranches = [];\n    this.branchnoResult = false;\n    this.branchtypeaheadLoading = false;\n    this.AgencyName = '';\n    this.selectedAgencyId = '';\n    this.agencyList = [];\n    this.agencynoResult = false;\n    this.agencytypeaheadLoading = false;\n    this.results = {};\n    this.showChart = false;\n  }\n  ngOnInit() {\n    this.buildFormGroup();\n    this.initializeData();\n  }\n  ngAfterViewInit() {\n    this.searchForm.addControl('products', this.productHierarchy.formGroup);\n    this.searchForm.addControl('buckets', this.bucketHierarchy.formGroup);\n    this.searchForm.addControl('geos', this.geoHierarchy.formGroup);\n    this.searchForm.updateValueAndValidity();\n  }\n  buildFormGroup() {\n    this.searchForm = new FormGroup({\n      allocationOwners: new FormControl([])\n    });\n  }\n  initializeData() {\n    try {\n      this.userDetails = JSON.parse(window.localStorage['currentUser'] || '{}');\n    } catch {\n      this.userDetails = {};\n    }\n    this.reportService.getBaseBranches().subscribe(basebranches => this.basebranches = basebranches, err => this.toastr.error(err, 'Error!'));\n    this.getAgencyList();\n  }\n  resetvaluesNew() {\n    this.staff = '';\n    this.selectedOwnerId = '';\n    this.branchName = '';\n    this.selectedBranchId = '';\n    this.AgencyName = '';\n    this.selectedAgencyId = '';\n  }\n  generatePayload() {\n    const fValue = this.searchForm?.value;\n    const obj = {};\n    const productLevels = Object.entries(fValue?.products || {}).filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0).map(([levelId, masterId]) => ({\n      levelId,\n      masterId\n    }));\n    if (productLevels.length) {\n      obj.products = {\n        levels: productLevels\n      };\n    }\n    const geoLevels = Object.entries(fValue?.geos || {}).filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0).map(([levelId, masterId]) => ({\n      levelId,\n      masterId\n    }));\n    if (geoLevels.length) {\n      obj.geos = {\n        levels: geoLevels\n      };\n    }\n    if (fValue?.buckets?.bucket?.length) {\n      obj.buckets = fValue.buckets.bucket;\n    }\n    if (this.selectedOwnerId) {\n      obj.allocationOwners = [this.selectedOwnerId];\n    }\n    const otherKeys = Object.keys(fValue || {}).filter(k => ![\"products\", \"geos\", \"buckets\", \"allocationOwners\"].includes(k));\n    otherKeys.forEach(k => {\n      if (fValue[k] !== null && fValue[k]?.length > 0) {\n        obj[k] = fValue[k];\n      }\n    });\n    return obj;\n  }\n  getSupervisorList(term = '') {\n    if (!term.trim()) {\n      this.bankUserList = of([]);\n      return;\n    }\n    this.bankUserList = this.reportService.getSupervisor(term);\n  }\n  onSelectStaff(event) {\n    this.staff = `${event.item.firstName} - ${event.item.agencyCode}`;\n    this.selectedOwnerId = event.item.id;\n  }\n  ownerNameEmpty() {\n    this.staff = '';\n    this.selectedOwnerId = '';\n  }\n  typeaheadNoResults(event) {\n    if (event) this.toastr.info('Please enter correct supervising manager', 'Info!');\n  }\n  onBranchSelect(event) {\n    this.branchName = event.item.name;\n    this.selectedBranchId = event.item.id;\n  }\n  branchNameEmpty() {\n    this.branchName = '';\n    this.selectedBranchId = '';\n  }\n  branchNoResults(event) {\n    this.branchnoResult = event;\n  }\n  branchChangeLoading(event) {\n    this.branchtypeaheadLoading = event;\n  }\n  getAgencyList() {\n    this.reportService.getFieldTeleAgencyName().subscribe(response => {\n      this.agencyList = response;\n      const agencyRoles = ['AgencyToBackEndExternalBIAP', 'AgencyToFrontEndExternalBIAP', 'AgencyToFrontEndExternalFOS', 'AgencyToFrontEndExternalTC'];\n      const role = this.userService.getPrimaryRole();\n      if (agencyRoles.includes(role)) {\n        this.agencyUser = true;\n        this.reportType = 'agency';\n        const abc = `${this.userDetails.agencyFirstName} ${this.userDetails.agencyLastName} ${this.userDetails.agencyCode}`;\n        this.AgencyName = abc.replace('null', '');\n        this.selectedAgencyId = this.userDetails.agencyId;\n      } else {\n        this.agencyUser = false;\n      }\n    }, err => this.toastr.error(err, 'Error!'));\n  }\n  onAgencySelect(event) {\n    this.AgencyName = `${event.item.firstName}-${event.item.agencyCode}`;\n    this.selectedAgencyId = event.item.id;\n  }\n  agencyNameEmpty() {\n    this.AgencyName = '';\n    this.selectedAgencyId = '';\n  }\n  agencyNoResults(event) {\n    this.agencynoResult = event;\n  }\n  agencyChangeLoading(event) {\n    this.agencytypeaheadLoading = event;\n  }\n  generateReport() {\n    const payload = this.generatePayload();\n    console.log(payload);\n    this.loader.generateReport = true;\n    this.reportService.generateAgentGap(payload).subscribe({\n      next: response => {\n        this.loader.generateReport = false;\n        console.log('Report data:', response);\n        if (response[\"allocatedCount\"] == 0 && response[\"unAllocatedCount\"] == 0) {\n          this.toastr.info('No results found!', \"Info!\");\n          this.showChart = false;\n        } else {\n          this.results = response;\n          this.showChart = true;\n          setTimeout(() => {\n            this.createChart1();\n          }, 100);\n        }\n      },\n      error: err => {\n        this.loader.generateReport = false;\n        this.toastr.error(err?.message || 'Something went wrong', 'Error!');\n      }\n    });\n  }\n  createChart1() {\n    const chartData = {\n      title: \"Agent Allocated & Un-Allocated Accounts Pie Report\",\n      data: [{\n        trailStatus: \"Allocated\",\n        noOfAccounts: this.results?.allocatedCount ?? 0\n      }, {\n        trailStatus: \"Un-Allocated\",\n        noOfAccounts: this.results?.unAllocatedCount ?? 0\n      }]\n    };\n    const svg = d3.select(\"svg\");\n    svg.selectAll(\"*\").remove();\n    const width = 960;\n    const height = 500;\n    const chartWidth = 400;\n    const chartHeight = 400;\n    const radius = Math.min(chartWidth, chartHeight) / 2;\n    const colorScale = d3.scaleOrdinal().domain([\"Allocated\", \"Un-Allocated\"]).range([\"#27ae60\", \"#9b59b6\"]);\n    const arc = d3.arc().outerRadius(radius - 25).innerRadius(1.5);\n    const pie = d3.pie().sort(null).value(d => d.noOfAccounts).padAngle(0.01);\n    // Add title at the top\n    svg.append(\"text\").attr(\"x\", width / 2).attr(\"y\", 30).style(\"text-anchor\", \"middle\").style(\"font\", \"16px sans-serif\").style(\"font-weight\", \"bold\").text(chartData.title);\n    // Position chart on the left side\n    const arcGroup = svg.append(\"g\").attr(\"class\", \"arc-group\").attr(\"transform\", `translate(${chartWidth / 2 + 50}, ${height / 2})`);\n    const arcs = arcGroup.selectAll(\".arc\").data(pie(chartData.data)).enter().append(\"g\").attr(\"class\", \"arc\");\n    arcs.append(\"path\").attr(\"d\", arc).attr(\"fill\", d => colorScale(d.data.trailStatus)).on(\"mouseover\", (event, d) => {\n      d3.select(\"#tooltip\").style(\"display\", \"inline-block\").style(\"left\", event.pageX + \"px\").style(\"top\", event.pageY + \"px\").style(\"opacity\", 1).select(\"#value\").html(`<b>Status:</b> ${d.data.trailStatus}, <b>Value:</b> ${d.data.noOfAccounts}`);\n    }).on(\"mouseout\", () => {\n      d3.select(\"#tooltip\").style(\"display\", \"none\");\n    });\n    // Position legend on the right side, vertically centered\n    const legendBox = svg.append(\"g\").attr(\"class\", \"legend-group\").attr(\"transform\", `translate(${chartWidth + 150}, ${height / 2 - chartData.data.length * 25 / 2})`);\n    const legend = legendBox.selectAll(\".legend\").data(chartData.data).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (d, i) => `translate(0, ${i * 25})`);\n    legend.append(\"circle\").attr(\"r\", 8).attr(\"fill\", d => colorScale(d.trailStatus));\n    legend.append(\"text\").attr(\"x\", 15).attr(\"y\", 5).style(\"font\", \"14px sans-serif\").text(d => `${d.trailStatus} (${d.noOfAccounts})`);\n  }\n  static {\n    this.propDecorators = {\n      productHierarchy: [{\n        type: ViewChild,\n        args: ['pr']\n      }],\n      bucketHierarchy: [{\n        type: ViewChild,\n        args: ['buc']\n      }],\n      geoHierarchy: [{\n        type: ViewChild,\n        args: ['geo']\n      }]\n    };\n  }\n};\nTrailGapComponent = __decorate([Component({\n  selector: 'app-trail-gap',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, BreadcrumbComponent, HierarchyFormDirective, HierarchyFormFieldComponent, TypeaheadModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], TrailGapComponent);\nexport { TrailGapComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormControl", "FormGroup", "FormsModule", "ReactiveFormsModule", "CommonModule", "ACMP", "AcmService", "BreadcrumbComponent", "HierarchyFormFieldComponent", "HierarchyFormDirective", "ReportService", "of", "ToastrService", "TypeaheadModule", "UserService", "d3", "TrailGapComponent", "constructor", "acmService", "reportService", "toastr", "userService", "breadcrumbData", "label", "canDownloadReport", "hasACMAccess", "CanDownloadAgentAllocationGapReport", "loader", "generateReport", "downloadReport", "reportType", "agencyUser", "staff", "selectedOwnerId", "bankUserList", "ownertypeaheadLoading", "branchName", "selectedBranchId", "basebranches", "branchnoResult", "branchtypeaheadLoading", "AgencyName", "selectedAgencyId", "agencyList", "agencynoResult", "agencytypeaheadLoading", "results", "showChart", "ngOnInit", "buildFormGroup", "initializeData", "ngAfterViewInit", "searchForm", "addControl", "productHierarchy", "formGroup", "bucketHierarchy", "geoHierarchy", "updateValueAndValidity", "allocationOwners", "userDetails", "JSON", "parse", "window", "localStorage", "getBaseBranches", "subscribe", "err", "error", "getAgencyList", "resetvalues<PERSON>ew", "generatePayload", "fValue", "value", "obj", "productLevels", "Object", "entries", "products", "filter", "_", "masterId", "Array", "isArray", "length", "map", "levelId", "levels", "geoLevels", "geos", "buckets", "bucket", "otherKeys", "keys", "k", "includes", "for<PERSON>ach", "getSupervisorList", "term", "trim", "getSupervisor", "onSelectStaff", "event", "item", "firstName", "agencyCode", "id", "ownerNameEmpty", "typeaheadNoResults", "info", "onBranchSelect", "name", "branchNameEmpty", "branchNoResults", "branchChangeLoading", "getFieldTeleAgencyName", "response", "agencyRoles", "role", "getPrimaryRole", "abc", "agencyFirstName", "agencyLastName", "replace", "agencyId", "onAgencySelect", "agencyNameEmpty", "agencyNoResults", "agencyChangeLoading", "payload", "console", "log", "generateAgentGap", "next", "setTimeout", "createChart1", "message", "chartData", "title", "data", "trailStatus", "noOfAccounts", "allocatedCount", "unAllocatedCount", "svg", "select", "selectAll", "remove", "width", "height", "chartWidth", "chartHeight", "radius", "Math", "min", "colorScale", "scaleOrdinal", "domain", "range", "arc", "outerRadius", "innerRadius", "pie", "sort", "d", "padAngle", "append", "attr", "style", "text", "arcGroup", "arcs", "enter", "on", "pageX", "pageY", "html", "legendBox", "legend", "i", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\github\\sowreports\\SCB-ENCollect.FE.Sleek\\src\\app\\reports\\pages\\trail-gap\\trail-gap.component.ts"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  Component,\r\n  inject,\r\n  OnInit,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport {\r\n  FormControl,\r\n  FormGroup,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from '@angular/forms';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ACMP, AcmService } from 'src/app/shared';\r\nimport { BreadcrumbComponent } from 'src/app/shared/components/breadcrumb/breadcrumb.component';\r\nimport { HierarchyFormFieldComponent } from 'src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component';\r\nimport { HierarchyFormDirective } from 'src/app/shared/directives/hierarchy-form.directive';\r\nimport { ReportService } from '../../reports.service';\r\nimport { Observable, of } from 'rxjs';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { TypeaheadModule } from 'ngx-bootstrap/typeahead';\r\nimport { UserService } from 'src/app/authentication/user.service';\r\nimport * as d3 from 'd3';\r\n\r\ninterface Datum {\r\n  trailStatus: string;\r\n  noOfAccounts: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-trail-gap',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    BreadcrumbComponent,\r\n    HierarchyFormDirective,\r\n    HierarchyFormFieldComponent,\r\n    TypeaheadModule,\r\n  ],\r\n  templateUrl: './trail-gap.component.html',\r\n  styleUrl: './trail-gap.component.scss',\r\n})\r\nexport class TrailGapComponent implements OnInit, AfterViewInit {\r\n  private acmService = inject(AcmService);\r\n  private reportService = inject(ReportService);\r\n  private toastr = inject(ToastrService);\r\n  private userService = inject(UserService);\r\n\r\n  @ViewChild('pr') productHierarchy!: HierarchyFormDirective;\r\n  @ViewChild('buc') bucketHierarchy!: HierarchyFormDirective;\r\n  @ViewChild('geo') geoHierarchy!: HierarchyFormDirective;\r\n\r\n  breadcrumbData = [\r\n    { label: 'Reports' },\r\n    { label: 'Allocation Reports' },\r\n    { label: 'Agent Allocation Gap Report' },\r\n  ];\r\n\r\n  canDownloadReport = this.acmService.hasACMAccess([\r\n    ACMP.CanDownloadAgentAllocationGapReport,\r\n  ]);\r\n\r\n  loader = {\r\n    generateReport: false,\r\n    downloadReport: false,\r\n  };\r\n\r\n  searchForm!: FormGroup;\r\n\r\n  reportType = 'bank';\r\n  agencyUser = false;\r\n\r\n  staff = '';\r\n  selectedOwnerId = '';\r\n\r\n  bankUserList: Observable<any[]> = of([]);\r\n  ownertypeaheadLoading = false;\r\n\r\n  branchName = '';\r\n  selectedBranchId = '';\r\n  basebranches: any[] = [];\r\n  branchnoResult = false;\r\n  branchtypeaheadLoading = false;\r\n\r\n  AgencyName = '';\r\n  selectedAgencyId = '';\r\n  agencyList: any[] = [];\r\n  agencynoResult = false;\r\n  agencytypeaheadLoading = false;\r\n\r\n  userDetails: any;\r\n  results: any = {};\r\n  showChart = false;\r\n\r\n  ngOnInit(): void {\r\n    this.buildFormGroup();\r\n    this.initializeData();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.searchForm.addControl('products', this.productHierarchy.formGroup);\r\n    this.searchForm.addControl('buckets', this.bucketHierarchy.formGroup);\r\n    this.searchForm.addControl('geos', this.geoHierarchy.formGroup);\r\n    this.searchForm.updateValueAndValidity();\r\n  }\r\n\r\n  buildFormGroup(): void {\r\n    this.searchForm = new FormGroup({\r\n      allocationOwners: new FormControl([]),\r\n    });\r\n  }\r\n\r\n  initializeData(): void {\r\n    try {\r\n      this.userDetails = JSON.parse(window.localStorage['currentUser'] || '{}');\r\n    } catch {\r\n      this.userDetails = {};\r\n    }\r\n\r\n    this.reportService.getBaseBranches().subscribe(\r\n      (basebranches) => (this.basebranches = basebranches),\r\n      (err) => this.toastr.error(err, 'Error!')\r\n    );\r\n\r\n    this.getAgencyList();\r\n  }\r\n\r\n  resetvaluesNew(): void {\r\n    this.staff = '';\r\n    this.selectedOwnerId = '';\r\n    this.branchName = '';\r\n    this.selectedBranchId = '';\r\n    this.AgencyName = '';\r\n    this.selectedAgencyId = '';\r\n  }\r\n\r\ngeneratePayload() {\r\n    const fValue = this.searchForm?.value;\r\n    const obj: any = {};\r\n\r\n    const productLevels = Object.entries(fValue?.products || {})\r\n      .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)\r\n      .map(([levelId, masterId]) => ({ levelId, masterId }));\r\n    if (productLevels.length) {\r\n      obj.products = { levels: productLevels };\r\n    }\r\n\r\n    const geoLevels = Object.entries(fValue?.geos || {})\r\n      .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)\r\n      .map(([levelId, masterId]) => ({ levelId, masterId }));\r\n    if (geoLevels.length) {\r\n      obj.geos = { levels: geoLevels };\r\n    }\r\n\r\n    if (fValue?.buckets?.bucket?.length) {\r\n      obj.buckets = fValue.buckets.bucket;\r\n    }\r\n\r\n    if (this.selectedOwnerId) {\r\n      obj.allocationOwners = [this.selectedOwnerId];\r\n    }\r\n\r\n    const otherKeys = Object.keys(fValue || {}).filter(\r\n      (k) => ![\"products\", \"geos\", \"buckets\", \"allocationOwners\"].includes(k)\r\n    );\r\n\r\n    otherKeys.forEach((k) => {\r\n      if (fValue[k] !== null && fValue[k]?.length > 0) {\r\n        obj[k] = fValue[k];\r\n      }\r\n    });\r\n\r\n    return obj;\r\n  }\r\n  getSupervisorList(term = ''): void {\r\n    if (!term.trim()) {\r\n      this.bankUserList = of([]);\r\n      return;\r\n    }\r\n    this.bankUserList = this.reportService.getSupervisor(term);\r\n  }\r\n\r\n  onSelectStaff(event: any): void {\r\n    this.staff = `${event.item.firstName} - ${event.item.agencyCode}`;\r\n    this.selectedOwnerId = event.item.id;\r\n  }\r\n\r\n  ownerNameEmpty(): void {\r\n    this.staff = '';\r\n    this.selectedOwnerId = '';\r\n  }\r\n\r\n  typeaheadNoResults(event: boolean): void {\r\n    if (event) this.toastr.info('Please enter correct supervising manager', 'Info!');\r\n  }\r\n\r\n  onBranchSelect(event: any): void {\r\n    this.branchName = event.item.name;\r\n    this.selectedBranchId = event.item.id;\r\n  }\r\n\r\n  branchNameEmpty(): void {\r\n    this.branchName = '';\r\n    this.selectedBranchId = '';\r\n  }\r\n\r\n  branchNoResults(event: boolean): void {\r\n    this.branchnoResult = event;\r\n  }\r\n\r\n  branchChangeLoading(event: boolean): void {\r\n    this.branchtypeaheadLoading = event;\r\n  }\r\n\r\n  getAgencyList(): void {\r\n    this.reportService.getFieldTeleAgencyName().subscribe(\r\n      (response) => {\r\n        this.agencyList = response;\r\n        const agencyRoles = [\r\n          'AgencyToBackEndExternalBIAP',\r\n          'AgencyToFrontEndExternalBIAP',\r\n          'AgencyToFrontEndExternalFOS',\r\n          'AgencyToFrontEndExternalTC',\r\n        ];\r\n        const role = this.userService.getPrimaryRole();\r\n        if (agencyRoles.includes(role)) {\r\n          this.agencyUser = true;\r\n          this.reportType = 'agency';\r\n          const abc = `${this.userDetails.agencyFirstName} ${this.userDetails.agencyLastName} ${this.userDetails.agencyCode}`;\r\n          this.AgencyName = abc.replace('null', '');\r\n          this.selectedAgencyId = this.userDetails.agencyId;\r\n        } else {\r\n          this.agencyUser = false;\r\n        }\r\n      },\r\n      (err) => this.toastr.error(err, 'Error!')\r\n    );\r\n  }\r\n\r\n  onAgencySelect(event: any): void {\r\n    this.AgencyName = `${event.item.firstName}-${event.item.agencyCode}`;\r\n    this.selectedAgencyId = event.item.id;\r\n  }\r\n\r\n  agencyNameEmpty(): void {\r\n    this.AgencyName = '';\r\n    this.selectedAgencyId = '';\r\n  }\r\n\r\n  agencyNoResults(event: boolean): void {\r\n    this.agencynoResult = event;\r\n  }\r\n\r\n  agencyChangeLoading(event: boolean): void {\r\n    this.agencytypeaheadLoading = event;\r\n  }\r\n\r\n  generateReport(): void {\r\n    const payload = this.generatePayload();\r\n    console.log(payload);\r\n\r\n    this.loader.generateReport = true;\r\n\r\n    this.reportService.generateAgentGap(payload).subscribe({\r\n      next: (response) => {\r\n        this.loader.generateReport = false;\r\n        console.log('Report data:', response);\r\n\r\n        if (response[\"allocatedCount\"] == 0 && response[\"unAllocatedCount\"] == 0) {\r\n          this.toastr.info('No results found!', \"Info!\");\r\n          this.showChart = false;\r\n        } else {\r\n          this.results = response;\r\n          this.showChart = true;\r\n          setTimeout(() => {\r\n            this.createChart1();\r\n          }, 100);\r\n        }\r\n      },\r\n      error: (err) => {\r\n        this.loader.generateReport = false;\r\n        this.toastr.error(err?.message || 'Something went wrong', 'Error!');\r\n      },\r\n    });\r\n  }\r\n\r\n  createChart1(): void {\r\n    const chartData = {\r\n      title: \"Agent Allocated & Un-Allocated Accounts Pie Report\",\r\n      data: [\r\n        {\r\n          trailStatus: \"Allocated\",\r\n          noOfAccounts: this.results?.allocatedCount ?? 0,\r\n        },\r\n        {\r\n          trailStatus: \"Un-Allocated\",\r\n          noOfAccounts: this.results?.unAllocatedCount ?? 0,\r\n        },\r\n      ],\r\n    };\r\n\r\n    const svg = d3.select(\"svg\");\r\n    svg.selectAll(\"*\").remove();\r\n\r\n    const width = 960;\r\n    const height = 500;\r\n    const chartWidth = 400;\r\n    const chartHeight = 400;\r\n    const radius = Math.min(chartWidth, chartHeight) / 2;\r\n\r\n    const colorScale = d3\r\n      .scaleOrdinal<string>()\r\n      .domain([\"Allocated\", \"Un-Allocated\"])\r\n      .range([\"#27ae60\", \"#9b59b6\"]);\r\n\r\n    const arc = d3.arc<any>().outerRadius(radius - 25).innerRadius(1.5);\r\n\r\n    const pie = d3\r\n      .pie<any>()\r\n      .sort(null)\r\n      .value((d) => d.noOfAccounts)\r\n      .padAngle(0.01);\r\n\r\n    // Add title at the top\r\n    svg\r\n      .append(\"text\")\r\n      .attr(\"x\", width / 2)\r\n      .attr(\"y\", 30)\r\n      .style(\"text-anchor\", \"middle\")\r\n      .style(\"font\", \"16px sans-serif\")\r\n      .style(\"font-weight\", \"bold\")\r\n      .text(chartData.title);\r\n\r\n    // Position chart on the left side\r\n    const arcGroup = svg\r\n      .append(\"g\")\r\n      .attr(\"class\", \"arc-group\")\r\n      .attr(\"transform\", `translate(${chartWidth / 2 + 50}, ${height / 2})`);\r\n\r\n    const arcs = arcGroup\r\n      .selectAll(\".arc\")\r\n      .data(pie(chartData.data))\r\n      .enter()\r\n      .append(\"g\")\r\n      .attr(\"class\", \"arc\");\r\n\r\n    arcs\r\n      .append(\"path\")\r\n      .attr(\"d\", <any>arc)\r\n      .attr(\"fill\", (d) => colorScale(d.data.trailStatus))\r\n      .on(\"mouseover\", (event, d) => {\r\n        d3.select(\"#tooltip\")\r\n          .style(\"display\", \"inline-block\")\r\n          .style(\"left\", event.pageX + \"px\")\r\n          .style(\"top\", event.pageY + \"px\")\r\n          .style(\"opacity\", 1)\r\n          .select(\"#value\")\r\n          .html(\r\n            `<b>Status:</b> ${d.data.trailStatus}, <b>Value:</b> ${d.data.noOfAccounts}`\r\n          );\r\n      })\r\n      .on(\"mouseout\", () => {\r\n        d3.select(\"#tooltip\").style(\"display\", \"none\");\r\n      });\r\n\r\n    // Position legend on the right side, vertically centered\r\n    const legendBox = svg\r\n      .append(\"g\")\r\n      .attr(\"class\", \"legend-group\")\r\n      .attr(\"transform\", `translate(${chartWidth + 150}, ${height / 2 - (chartData.data.length * 25) / 2})`);\r\n\r\n    const legend = legendBox\r\n      .selectAll(\".legend\")\r\n      .data(chartData.data)\r\n      .enter()\r\n      .append(\"g\")\r\n      .attr(\"class\", \"legend\")\r\n      .attr(\"transform\", (d, i) => `translate(0, ${i * 25})`);\r\n\r\n    legend\r\n      .append(\"circle\")\r\n      .attr(\"r\", 8)\r\n      .attr(\"fill\", (d) => colorScale(d.trailStatus));\r\n\r\n    legend\r\n      .append(\"text\")\r\n      .attr(\"x\", 15)\r\n      .attr(\"y\", 5)\r\n      .style(\"font\", \"14px sans-serif\")\r\n      .text((d) => `${d.trailStatus} (${d.noOfAccounts})`);\r\n}\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAEEA,SAAS,EACTC,MAAM,EAENC,SAAS,QACJ,eAAe;AACtB,SACEC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,IAAI,EAAEC,UAAU,QAAQ,gBAAgB;AACjD,SAASC,mBAAmB,QAAQ,2DAA2D;AAC/F,SAASC,2BAA2B,QAAQ,+EAA+E;AAC3H,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAAqBC,EAAE,QAAQ,MAAM;AACrC,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,WAAW,QAAQ,qCAAqC;AACjE,OAAO,KAAKC,EAAE,MAAM,IAAI;AAsBjB,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAAvBC,YAAA;IACG,KAAAC,UAAU,GAAGpB,MAAM,CAACQ,UAAU,CAAC;IAC/B,KAAAa,aAAa,GAAGrB,MAAM,CAACY,aAAa,CAAC;IACrC,KAAAU,MAAM,GAAGtB,MAAM,CAACc,aAAa,CAAC;IAC9B,KAAAS,WAAW,GAAGvB,MAAM,CAACgB,WAAW,CAAC;IAMzC,KAAAQ,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpB;MAAEA,KAAK,EAAE;IAAoB,CAAE,EAC/B;MAAEA,KAAK,EAAE;IAA6B,CAAE,CACzC;IAED,KAAAC,iBAAiB,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,CAAC,CAC/CpB,IAAI,CAACqB,mCAAmC,CACzC,CAAC;IAEF,KAAAC,MAAM,GAAG;MACPC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE;KACjB;IAID,KAAAC,UAAU,GAAG,MAAM;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,eAAe,GAAG,EAAE;IAEpB,KAAAC,YAAY,GAAsBvB,EAAE,CAAC,EAAE,CAAC;IACxC,KAAAwB,qBAAqB,GAAG,KAAK;IAE7B,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,sBAAsB,GAAG,KAAK;IAE9B,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,sBAAsB,GAAG,KAAK;IAG9B,KAAAC,OAAO,GAAQ,EAAE;IACjB,KAAAC,SAAS,GAAG,KAAK;EA4SnB;EA1SEC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,CAACC,UAAU,CAAC,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAACC,SAAS,CAAC;IACvE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,SAAS,EAAE,IAAI,CAACG,eAAe,CAACD,SAAS,CAAC;IACrE,IAAI,CAACH,UAAU,CAACC,UAAU,CAAC,MAAM,EAAE,IAAI,CAACI,YAAY,CAACF,SAAS,CAAC;IAC/D,IAAI,CAACH,UAAU,CAACM,sBAAsB,EAAE;EAC1C;EAEAT,cAAcA,CAAA;IACZ,IAAI,CAACG,UAAU,GAAG,IAAInD,SAAS,CAAC;MAC9B0D,gBAAgB,EAAE,IAAI3D,WAAW,CAAC,EAAE;KACrC,CAAC;EACJ;EAEAkD,cAAcA,CAAA;IACZ,IAAI;MACF,IAAI,CAACU,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;IAC3E,CAAC,CAAC,MAAM;MACN,IAAI,CAACJ,WAAW,GAAG,EAAE;IACvB;IAEA,IAAI,CAACzC,aAAa,CAAC8C,eAAe,EAAE,CAACC,SAAS,CAC3C5B,YAAY,IAAM,IAAI,CAACA,YAAY,GAAGA,YAAa,EACnD6B,GAAG,IAAK,IAAI,CAAC/C,MAAM,CAACgD,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC,CAC1C;IAED,IAAI,CAACE,aAAa,EAAE;EACtB;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACtC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACG,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACI,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC5B;EAEF6B,eAAeA,CAAA;IACX,MAAMC,MAAM,GAAG,IAAI,CAACpB,UAAU,EAAEqB,KAAK;IACrC,MAAMC,GAAG,GAAQ,EAAE;IAEnB,MAAMC,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACL,MAAM,EAAEM,QAAQ,IAAI,EAAE,CAAC,CACzDC,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,QAAQ,CAAC,KAAKC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,CACzEC,GAAG,CAAC,CAAC,CAACC,OAAO,EAAEL,QAAQ,CAAC,MAAM;MAAEK,OAAO;MAAEL;IAAQ,CAAE,CAAC,CAAC;IACxD,IAAIN,aAAa,CAACS,MAAM,EAAE;MACxBV,GAAG,CAACI,QAAQ,GAAG;QAAES,MAAM,EAAEZ;MAAa,CAAE;IAC1C;IAEA,MAAMa,SAAS,GAAGZ,MAAM,CAACC,OAAO,CAACL,MAAM,EAAEiB,IAAI,IAAI,EAAE,CAAC,CACjDV,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,QAAQ,CAAC,KAAKC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,CACzEC,GAAG,CAAC,CAAC,CAACC,OAAO,EAAEL,QAAQ,CAAC,MAAM;MAAEK,OAAO;MAAEL;IAAQ,CAAE,CAAC,CAAC;IACxD,IAAIO,SAAS,CAACJ,MAAM,EAAE;MACpBV,GAAG,CAACe,IAAI,GAAG;QAAEF,MAAM,EAAEC;MAAS,CAAE;IAClC;IAEA,IAAIhB,MAAM,EAAEkB,OAAO,EAAEC,MAAM,EAAEP,MAAM,EAAE;MACnCV,GAAG,CAACgB,OAAO,GAAGlB,MAAM,CAACkB,OAAO,CAACC,MAAM;IACrC;IAEA,IAAI,IAAI,CAAC1D,eAAe,EAAE;MACxByC,GAAG,CAACf,gBAAgB,GAAG,CAAC,IAAI,CAAC1B,eAAe,CAAC;IAC/C;IAEA,MAAM2D,SAAS,GAAGhB,MAAM,CAACiB,IAAI,CAACrB,MAAM,IAAI,EAAE,CAAC,CAACO,MAAM,CAC/Ce,CAAC,IAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAACC,QAAQ,CAACD,CAAC,CAAC,CACxE;IAEDF,SAAS,CAACI,OAAO,CAAEF,CAAC,IAAI;MACtB,IAAItB,MAAM,CAACsB,CAAC,CAAC,KAAK,IAAI,IAAItB,MAAM,CAACsB,CAAC,CAAC,EAAEV,MAAM,GAAG,CAAC,EAAE;QAC/CV,GAAG,CAACoB,CAAC,CAAC,GAAGtB,MAAM,CAACsB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAOpB,GAAG;EACZ;EACAuB,iBAAiBA,CAACC,IAAI,GAAG,EAAE;IACzB,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE,EAAE;MAChB,IAAI,CAACjE,YAAY,GAAGvB,EAAE,CAAC,EAAE,CAAC;MAC1B;IACF;IACA,IAAI,CAACuB,YAAY,GAAG,IAAI,CAACf,aAAa,CAACiF,aAAa,CAACF,IAAI,CAAC;EAC5D;EAEAG,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACtE,KAAK,GAAG,GAAGsE,KAAK,CAACC,IAAI,CAACC,SAAS,MAAMF,KAAK,CAACC,IAAI,CAACE,UAAU,EAAE;IACjE,IAAI,CAACxE,eAAe,GAAGqE,KAAK,CAACC,IAAI,CAACG,EAAE;EACtC;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC3E,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EAEA2E,kBAAkBA,CAACN,KAAc;IAC/B,IAAIA,KAAK,EAAE,IAAI,CAAClF,MAAM,CAACyF,IAAI,CAAC,0CAA0C,EAAE,OAAO,CAAC;EAClF;EAEAC,cAAcA,CAACR,KAAU;IACvB,IAAI,CAAClE,UAAU,GAAGkE,KAAK,CAACC,IAAI,CAACQ,IAAI;IACjC,IAAI,CAAC1E,gBAAgB,GAAGiE,KAAK,CAACC,IAAI,CAACG,EAAE;EACvC;EAEAM,eAAeA,CAAA;IACb,IAAI,CAAC5E,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC5B;EAEA4E,eAAeA,CAACX,KAAc;IAC5B,IAAI,CAAC/D,cAAc,GAAG+D,KAAK;EAC7B;EAEAY,mBAAmBA,CAACZ,KAAc;IAChC,IAAI,CAAC9D,sBAAsB,GAAG8D,KAAK;EACrC;EAEAjC,aAAaA,CAAA;IACX,IAAI,CAAClD,aAAa,CAACgG,sBAAsB,EAAE,CAACjD,SAAS,CAClDkD,QAAQ,IAAI;MACX,IAAI,CAACzE,UAAU,GAAGyE,QAAQ;MAC1B,MAAMC,WAAW,GAAG,CAClB,6BAA6B,EAC7B,8BAA8B,EAC9B,6BAA6B,EAC7B,4BAA4B,CAC7B;MACD,MAAMC,IAAI,GAAG,IAAI,CAACjG,WAAW,CAACkG,cAAc,EAAE;MAC9C,IAAIF,WAAW,CAACtB,QAAQ,CAACuB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACvF,UAAU,GAAG,IAAI;QACtB,IAAI,CAACD,UAAU,GAAG,QAAQ;QAC1B,MAAM0F,GAAG,GAAG,GAAG,IAAI,CAAC5D,WAAW,CAAC6D,eAAe,IAAI,IAAI,CAAC7D,WAAW,CAAC8D,cAAc,IAAI,IAAI,CAAC9D,WAAW,CAAC6C,UAAU,EAAE;QACnH,IAAI,CAAChE,UAAU,GAAG+E,GAAG,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QACzC,IAAI,CAACjF,gBAAgB,GAAG,IAAI,CAACkB,WAAW,CAACgE,QAAQ;MACnD,CAAC,MAAM;QACL,IAAI,CAAC7F,UAAU,GAAG,KAAK;MACzB;IACF,CAAC,EACAoC,GAAG,IAAK,IAAI,CAAC/C,MAAM,CAACgD,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC,CAC1C;EACH;EAEA0D,cAAcA,CAACvB,KAAU;IACvB,IAAI,CAAC7D,UAAU,GAAG,GAAG6D,KAAK,CAACC,IAAI,CAACC,SAAS,IAAIF,KAAK,CAACC,IAAI,CAACE,UAAU,EAAE;IACpE,IAAI,CAAC/D,gBAAgB,GAAG4D,KAAK,CAACC,IAAI,CAACG,EAAE;EACvC;EAEAoB,eAAeA,CAAA;IACb,IAAI,CAACrF,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC5B;EAEAqF,eAAeA,CAACzB,KAAc;IAC5B,IAAI,CAAC1D,cAAc,GAAG0D,KAAK;EAC7B;EAEA0B,mBAAmBA,CAAC1B,KAAc;IAChC,IAAI,CAACzD,sBAAsB,GAAGyD,KAAK;EACrC;EAEA1E,cAAcA,CAAA;IACZ,MAAMqG,OAAO,GAAG,IAAI,CAAC1D,eAAe,EAAE;IACtC2D,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IAEpB,IAAI,CAACtG,MAAM,CAACC,cAAc,GAAG,IAAI;IAEjC,IAAI,CAACT,aAAa,CAACiH,gBAAgB,CAACH,OAAO,CAAC,CAAC/D,SAAS,CAAC;MACrDmE,IAAI,EAAGjB,QAAQ,IAAI;QACjB,IAAI,CAACzF,MAAM,CAACC,cAAc,GAAG,KAAK;QAClCsG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEf,QAAQ,CAAC;QAErC,IAAIA,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAIA,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;UACxE,IAAI,CAAChG,MAAM,CAACyF,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC;UAC9C,IAAI,CAAC9D,SAAS,GAAG,KAAK;QACxB,CAAC,MAAM;UACL,IAAI,CAACD,OAAO,GAAGsE,QAAQ;UACvB,IAAI,CAACrE,SAAS,GAAG,IAAI;UACrBuF,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,YAAY,EAAE;UACrB,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC;MACDnE,KAAK,EAAGD,GAAG,IAAI;QACb,IAAI,CAACxC,MAAM,CAACC,cAAc,GAAG,KAAK;QAClC,IAAI,CAACR,MAAM,CAACgD,KAAK,CAACD,GAAG,EAAEqE,OAAO,IAAI,sBAAsB,EAAE,QAAQ,CAAC;MACrE;KACD,CAAC;EACJ;EAEAD,YAAYA,CAAA;IACV,MAAME,SAAS,GAAG;MAChBC,KAAK,EAAE,oDAAoD;MAC3DC,IAAI,EAAE,CACJ;QACEC,WAAW,EAAE,WAAW;QACxBC,YAAY,EAAE,IAAI,CAAC/F,OAAO,EAAEgG,cAAc,IAAI;OAC/C,EACD;QACEF,WAAW,EAAE,cAAc;QAC3BC,YAAY,EAAE,IAAI,CAAC/F,OAAO,EAAEiG,gBAAgB,IAAI;OACjD;KAEJ;IAED,MAAMC,GAAG,GAAGjI,EAAE,CAACkI,MAAM,CAAC,KAAK,CAAC;IAC5BD,GAAG,CAACE,SAAS,CAAC,GAAG,CAAC,CAACC,MAAM,EAAE;IAE3B,MAAMC,KAAK,GAAG,GAAG;IACjB,MAAMC,MAAM,GAAG,GAAG;IAClB,MAAMC,UAAU,GAAG,GAAG;IACtB,MAAMC,WAAW,GAAG,GAAG;IACvB,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,UAAU,EAAEC,WAAW,CAAC,GAAG,CAAC;IAEpD,MAAMI,UAAU,GAAG5I,EAAE,CAClB6I,YAAY,EAAU,CACtBC,MAAM,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CACrCC,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAEhC,MAAMC,GAAG,GAAGhJ,EAAE,CAACgJ,GAAG,EAAO,CAACC,WAAW,CAACR,MAAM,GAAG,EAAE,CAAC,CAACS,WAAW,CAAC,GAAG,CAAC;IAEnE,MAAMC,GAAG,GAAGnJ,EAAE,CACXmJ,GAAG,EAAO,CACVC,IAAI,CAAC,IAAI,CAAC,CACV1F,KAAK,CAAE2F,CAAC,IAAKA,CAAC,CAACvB,YAAY,CAAC,CAC5BwB,QAAQ,CAAC,IAAI,CAAC;IAEjB;IACArB,GAAG,CACAsB,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,GAAG,EAAEnB,KAAK,GAAG,CAAC,CAAC,CACpBmB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CACbC,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC9BA,KAAK,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAChCA,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,CAC5BC,IAAI,CAAChC,SAAS,CAACC,KAAK,CAAC;IAExB;IACA,MAAMgC,QAAQ,GAAG1B,GAAG,CACjBsB,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAC1BA,IAAI,CAAC,WAAW,EAAE,aAAajB,UAAU,GAAG,CAAC,GAAG,EAAE,KAAKD,MAAM,GAAG,CAAC,GAAG,CAAC;IAExE,MAAMsB,IAAI,GAAGD,QAAQ,CAClBxB,SAAS,CAAC,MAAM,CAAC,CACjBP,IAAI,CAACuB,GAAG,CAACzB,SAAS,CAACE,IAAI,CAAC,CAAC,CACzBiC,KAAK,EAAE,CACPN,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;IAEvBI,IAAI,CACDL,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,GAAG,EAAOR,GAAG,CAAC,CACnBQ,IAAI,CAAC,MAAM,EAAGH,CAAC,IAAKT,UAAU,CAACS,CAAC,CAACzB,IAAI,CAACC,WAAW,CAAC,CAAC,CACnDiC,EAAE,CAAC,WAAW,EAAE,CAACvE,KAAK,EAAE8D,CAAC,KAAI;MAC5BrJ,EAAE,CAACkI,MAAM,CAAC,UAAU,CAAC,CAClBuB,KAAK,CAAC,SAAS,EAAE,cAAc,CAAC,CAChCA,KAAK,CAAC,MAAM,EAAElE,KAAK,CAACwE,KAAK,GAAG,IAAI,CAAC,CACjCN,KAAK,CAAC,KAAK,EAAElE,KAAK,CAACyE,KAAK,GAAG,IAAI,CAAC,CAChCP,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBvB,MAAM,CAAC,QAAQ,CAAC,CAChB+B,IAAI,CACH,kBAAkBZ,CAAC,CAACzB,IAAI,CAACC,WAAW,mBAAmBwB,CAAC,CAACzB,IAAI,CAACE,YAAY,EAAE,CAC7E;IACL,CAAC,CAAC,CACDgC,EAAE,CAAC,UAAU,EAAE,MAAK;MACnB9J,EAAE,CAACkI,MAAM,CAAC,UAAU,CAAC,CAACuB,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC;IAChD,CAAC,CAAC;IAEJ;IACA,MAAMS,SAAS,GAAGjC,GAAG,CAClBsB,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAC7BA,IAAI,CAAC,WAAW,EAAE,aAAajB,UAAU,GAAG,GAAG,KAAKD,MAAM,GAAG,CAAC,GAAIZ,SAAS,CAACE,IAAI,CAACvD,MAAM,GAAG,EAAE,GAAI,CAAC,GAAG,CAAC;IAExG,MAAM8F,MAAM,GAAGD,SAAS,CACrB/B,SAAS,CAAC,SAAS,CAAC,CACpBP,IAAI,CAACF,SAAS,CAACE,IAAI,CAAC,CACpBiC,KAAK,EAAE,CACPN,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CACvBA,IAAI,CAAC,WAAW,EAAE,CAACH,CAAC,EAAEe,CAAC,KAAK,gBAAgBA,CAAC,GAAG,EAAE,GAAG,CAAC;IAEzDD,MAAM,CACHZ,MAAM,CAAC,QAAQ,CAAC,CAChBC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CACZA,IAAI,CAAC,MAAM,EAAGH,CAAC,IAAKT,UAAU,CAACS,CAAC,CAACxB,WAAW,CAAC,CAAC;IAEjDsC,MAAM,CACHZ,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CACbA,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CACZC,KAAK,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAChCC,IAAI,CAAEL,CAAC,IAAK,GAAGA,CAAC,CAACxB,WAAW,KAAKwB,CAAC,CAACvB,YAAY,GAAG,CAAC;EAC1D;;;;cAtVG9I,SAAS;QAAAqL,IAAA,GAAC,IAAI;MAAA;;cACdrL,SAAS;QAAAqL,IAAA,GAAC,KAAK;MAAA;;cACfrL,SAAS;QAAAqL,IAAA,GAAC,KAAK;MAAA;;;;AARLpK,iBAAiB,GAAAqK,UAAA,EAf7BxL,SAAS,CAAC;EACTyL,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPpL,YAAY,EACZF,WAAW,EACXC,mBAAmB,EACnBI,mBAAmB,EACnBE,sBAAsB,EACtBD,2BAA2B,EAC3BK,eAAe,CAChB;EACD4K,QAAA,EAAAC,oBAAyC;;CAE1C,CAAC,C,EACW1K,iBAAiB,CA8V7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}