import {
  AfterViewInit,
  Component,
  ViewChild,
  OnInit,
  inject,
} from "@angular/core";
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { CommonModule } from "@angular/common";
import { Observable, of } from "rxjs";
import { map } from "rxjs/operators";
import * as d3 from "d3";
import { TypeaheadModule } from "ngx-bootstrap/typeahead";
import { BsDropdownModule } from "ngx-bootstrap/dropdown";

import { ACMP, AcmService } from "src/app/shared";
import { BreadcrumbComponent } from "src/app/shared/components/breadcrumb/breadcrumb.component";
import { HierarchyFormFieldComponent } from "src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component";
import { HierarchyFormDirective } from "src/app/shared/directives/hierarchy-form.directive";
import { ReportService } from "../../reports.service";
import { ToastrService } from "ngx-toastr";

@Component({
  selector: "app-agency-allocation-gap",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BreadcrumbComponent,
    HierarchyFormDirective,
    HierarchyFormFieldComponent,
    TypeaheadModule,
    BsDropdownModule,
  ],
  templateUrl: "./agency-allocation-gap.component.html",
  styleUrl: "./agency-allocation-gap.component.scss",
})
export class AgencyAllocationGapComponent implements OnInit, AfterViewInit {
  private acmService = inject(AcmService);
  private reportService = inject(ReportService);
  private toastr = inject(ToastrService);

  @ViewChild("pr") productHierarchy!: HierarchyFormDirective;
  @ViewChild("buc") bucketHierarchy!: HierarchyFormDirective;
  @ViewChild("geo") geoHierarchy!: HierarchyFormDirective;

  searchForm!: FormGroup;
  bankUserList!: Observable<any[]>;
  noResultStaff = false;
  data: any = null;
  showChart = false;
  selectedOwnerId: any;

  breadcrumbData = [
    { label: "Reports" },
    { label: "Allocation Reports" },
    { label: "Agency Allocation Gap Report" },
  ];

  canDownloadReport = this.acmService.hasACMAccess([
    ACMP.CanDownloadAgencyAllocationGapReport,
  ]);

  loader = {
    generateReport: false,
    downloadReport: false,
  };

  ngOnInit(): void {
    this.searchForm = new FormGroup({
      allocationOwners: new FormControl(""), 
      staff: new FormControl(""),
    });
  }

  ngAfterViewInit(): void {
    this.searchForm.addControl("products", this.productHierarchy.formGroup);
    this.searchForm.addControl("buckets", this.bucketHierarchy.formGroup);
    this.searchForm.addControl("geos", this.geoHierarchy.formGroup);
    this.searchForm.updateValueAndValidity();
  }

  getSupervisorList(term: string): void {
    if (!term || term.trim().length < 1) {
      this.bankUserList = of([]);
      return;
    }
    this.bankUserList = this.reportService.getSupervisor(term);
  }

  onSelectStaff(event: any): void {
    const display = `${event.item.firstName} - ${event.item.agencyCode}`;
    this.searchForm.get("allocationOwners")?.setValue(display);
    this.selectedOwnerId = event.item.id;
  }

ownerNameEmpty(): void {
  const currentValue = this.searchForm.get("allocationOwners")?.value;
  if (!this.bankUserList || typeof currentValue === 'string') {
    this.searchForm.get("allocationOwners")?.setValue(null);
    this.selectedOwnerId = null; 
  }
}

  typeaheadNoResultsStaff(event: boolean): void {
    this.noResultStaff = event;
  }

  typeaheadNoResults(event: boolean): void {
    if (event) {
      this.toastr.info("Please enter correct supervising manager", "Info!");
    }
  }

  ownerInputFormatter = (item: any): string => item || "";

  generatePayload() {
    const fValue = this.searchForm?.value;
    const obj: any = {};

    const productLevels = Object.entries(fValue?.products || {})
      .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)
      .map(([levelId, masterId]) => ({ levelId, masterId }));
    if (productLevels.length) {
      obj.products = { levels: productLevels };
    }

    const geoLevels = Object.entries(fValue?.geos || {})
      .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)
      .map(([levelId, masterId]) => ({ levelId, masterId }));
    if (geoLevels.length) {
      obj.geos = { levels: geoLevels };
    }

    if (fValue?.buckets?.bucket?.length) {
      obj.buckets = fValue.buckets.bucket;
    }

    if (this.selectedOwnerId != null) {
      obj.allocationOwners = [this.selectedOwnerId];
    }

    const otherKeys = Object.keys(fValue || {}).filter(
      (k) => !["products", "geos", "buckets", "allocationOwners"].includes(k)
    );

    otherKeys.forEach((k) => {
      if (fValue[k] !== null && fValue[k]?.length > 0) {
        obj[k] = fValue[k];
      }
    });

    return obj;
  }

  generateReport(): void {
    const payload = this.generatePayload();
    this.loader.generateReport = true;

    this.reportService.generateAgencyGap(payload).subscribe({
      next: (response) => {
        this.loader.generateReport = false;
        const { allocatedCount, unAllocatedCount } = response;

        if (!allocatedCount && !unAllocatedCount) {
          this.toastr.info("No results found!", "Info!");
          this.showChart = false;
          this.data = null;
        } else {
          this.data = response;
          this.showChart = true;
          setTimeout(() => {
            this.createChart1();
          }, 100);
        }
      },
      error: (err) => {
        this.toastr.error(err?.message || "Something went wrong", "Error!");
        this.loader.generateReport = false;
      },
    });
  }

  createChart1(): void {
    const chartData = {
      title: "Agency Allocated & Un-Allocated Accounts Pie Report",
      data: [
        {
          trailStatus: "Allocated",
          noOfAccounts: this.data?.allocatedCount ?? 0,
        },
        {
          trailStatus: "Un-Allocated",
          noOfAccounts: this.data?.unAllocatedCount ?? 0,
        },
      ],
    };

    const svg = d3.select("svg");
    svg.selectAll("*").remove();

    const width = 960;
    const height = 500;
    const chartWidth = 400;
    const chartHeight = 400;
    const radius = Math.min(chartWidth, chartHeight) / 2;

    const colorScale = d3
      .scaleOrdinal<string>()
      .domain(["Allocated", "Un-Allocated"])
      .range(["#27ae60", "#9b59b6"]);

    const arc = d3.arc<any>().outerRadius(radius - 25).innerRadius(1.5);

    const pie = d3
      .pie<any>()
      .sort(null)
      .value((d) => d.noOfAccounts)
      .padAngle(0.01);

    // Add title at the top
    svg
      .append("text")
      .attr("x", width / 2)
      .attr("y", 30)
      .style("text-anchor", "middle")
      .style("font", "16px sans-serif")
      .style("font-weight", "bold")
      .text(chartData.title);

    // Position chart on the left side
    const arcGroup = svg
      .append("g")
      .attr("class", "arc-group")
      .attr("transform", `translate(${chartWidth / 2 + 50}, ${height / 2})`);

    const arcs = arcGroup
      .selectAll(".arc")
      .data(pie(chartData.data))
      .enter()
      .append("g")
      .attr("class", "arc");

    arcs
      .append("path")
      .attr("d", <any>arc)
      .attr("fill", (d) => colorScale(d.data.trailStatus))
      .on("mouseover", (event, d) => {
        d3.select("#tooltip")
          .style("display", "inline-block")
          .style("left", event.pageX + "px")
          .style("top", event.pageY + "px")
          .style("opacity", 1)
          .select("#value")
          .html(
            `<b>Status:</b> ${d.data.trailStatus}, <b>Value:</b> ${d.data.noOfAccounts}`
          );
      })
      .on("mouseout", () => {
        d3.select("#tooltip").style("display", "none");
      });

    // Position legend on the right side, vertically centered
    const legendBox = svg
      .append("g")
      .attr("class", "legend-group")
      .attr("transform", `translate(${chartWidth + 150}, ${height / 2 - (chartData.data.length * 25) / 2})`);

    const legend = legendBox
      .selectAll(".legend")
      .data(chartData.data)
      .enter()
      .append("g")
      .attr("class", "legend")
      .attr("transform", (d, i) => `translate(0, ${i * 25})`);

    legend
      .append("circle")
      .attr("r", 8)
      .attr("fill", (d) => colorScale(d.trailStatus));

    legend
      .append("text")
      .attr("x", 15)
      .attr("y", 5)
      .style("font", "14px sans-serif")
      .text((d) => `${d.trailStatus} (${d.noOfAccounts})`);
  }

  downloadReport() {
  const payload = this.generatePayload();
  this.reportService.downloadPrimaryAgency(payload).subscribe(
    response => {
      const fileName = response.filename;
      this.getdata(fileName);
    },
    err => {
      this.toastr.error(err, "Error!");
    }
  );
}

getdata(fileName: string) {
  this.reportService.downloadFile(fileName).subscribe(response => {
    if (response.length === 0) {
      this.toastr.warning('No results found!', "Warning!");
    } else {
      const mediaType = 'application/zip';
      const a = document.createElement("a");
      a.setAttribute('style', 'display:none;');
      document.body.appendChild(a);
      const blob = new Blob([response], { type: mediaType });
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = 'agencygap.zip';
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }
  });
}

}
