import { Injectable } from "@angular/core";
import { ApiService } from "../shared/services/api.service";

@Injectable({ providedIn: 'root' })
export class ReportService {
  constructor(private apiService: ApiService) {}

  // primary Allocation
  getProductGroupList() {
    return this.apiService.get(
      "api/mvp/get/primaryCategoryItems?categoryMasterId=ProductGroup"
    );
  }

  getProductGroupData(data) {
    return this.apiService.get(
      "api/mvp/get/primaryCategoryItems?categoryMasterId=" + data
    );
  }

  getProductListByPG(data) {
    return this.apiService.get(
      "api/mvp/get/secondaryCategoryItemByParentId?parentId=" + data
    );
  }

  getSubproductListByProduct(data) {
    return this.apiService.get(
      "api/mvp/get/secondaryCategoryItemByParentId?parentId=" + data
    );
  }

  getAllDespositionGroups() {
    return this.apiService.get("api/mvp/dispositiongroupmaster");
  }

  dispostionCode(val) {
    return this.apiService.post("api/mvp/dispositionCodemaster", val);
  }

  getBucketList() {
    return this.apiService.get("api/mvp/bucketmaster");
  }

  uploadFile(formData: FormData) {
    const options = {};
    return this.apiService.post("api/FileUpload", formData, options);
  }

  getAgencyName() {
    return this.apiService.get("api/mvp/Agency");
  }

  getFieldTeleAgencyName() {
    return this.apiService.get("api/mvp/agencylist");
  }

  getFieldTeleAgents(data) {
    return this.apiService.get("api/mvp/agencyuser/AgentByagencyId/" + data);
  }

  /*getAgencyName() {
    return this.apiService.get('api/mvp/agencylist')
      .pipe(
        catchError(error => this.formatErrors(error))
      );
  }*/

  getAgentName(data) {
    return this.apiService.get("api/mvp/Search/Agent/byname/" + data);
  }

  getAgentNameForFilter(data) {
    return this.apiService.get("api/mvp/Search/Agent/byname/" + data);
  }

  paymentStatusList() {
    return this.apiService.get(
      "api/mvp/get/primaryCategoryItems?categoryMasterId=PaymentMaster"
    );
  }

  getFiltersList() {
    return this.apiService.get("api/mvp/getallfilters");
  }

  // PaymentReport
  generatePaymentReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Payment/get/paymentEsDashboardRpt",
      data
    );
  }

  generatePaymentReportbyCBS(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Payment/get/paymentCBSEsDashboardRpt",
      data
    );
  }

  // moneymovementreport
  generateMoneyMovementReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/MoneyMovement/get/moneymovementEsDashboardRpt",
      data
    );
  }

  // tail gap
  generateTrailGap(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Trailgap/get/trailgapEsDashboardRpt",
      data
    );
  }

  // getTrailIntensityDetails() {
  //   return this.apiService.get('api/mvp/get/TrailIntensityDetails')
  //     .pipe(
  //       catchError(error => this.formatErrors(error))
  //     );
  // }

  // tail gap & intensity
  generateTrailIntensity(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/TrailIntensity/get/trailintensityEsDashboardRpt",
      data
    );
  }

  getAgentGapDetails() {
    return this.apiService.get("api/mvp/get/AgentDetails");
  }

  getAgencyGapDetails() {
    return this.apiService.get("api/mvp/get/AgencyDetails");
  }

  // getTrailGapDetails() {
  //   return this.apiService.get(this.baseURL+'/get/TrailGapDetails')
  //    .pipe(
  //       catchError(error => this.formatErrors(error))
  //     );
  // }

  getPaymentReportDetails() {
    return this.apiService.get("api/mvp/get/PaymentDetails");
  }

  getMoneyMovementReportDetails() {
    return this.apiService.get("api/mvp/get/MoneyMovementDetails");
  }

  downloadFile(data) {
    return this.apiService.getRawZipReports(
      "/ReportsCommon/EsGetFileDd/" + data
    );
  }

  primaryInsightAllocationDownload(data) {
    return this.apiService.getRawZipReports(
      "/ReportsCommon/EsGetFileDd/" + data
    );
  }

  downloadAgencyGap(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.post("api/mvp/get/agencyEsDownloadDd", data);
  }

  downloadAgentGap(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.post("api/mvp/get/agentEsDownloadDd", data);
  }

  downloadPaymentAllocation(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Payment/get/paymentEsDownloadRpt",
      data
    );
  }

  downloadPaymentAllocationCBS(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/payment/get/paymentCBSEsDownloadRpt",
      data
    );
  }

  downloadMoneyMovementAllocation(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/MoneyMovement/get/moneymovementEsDownloadRpt",
      data
    );
  }

  downloadTrailGapandIntensity(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Trailgap/get/trailgapEsDownloadRpt",
      data
    );
  }

  downloadTrailIntensity(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/TrailIntensity/get/TrailintensityEsDownloadRpt",
      data
    );
  }

  // agency gap
  generateAgencyGapAllocation(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/AgencyAllocationGap/get/agencyEsDashboardRpt",
      data
    );
  }

  generateAgencyAllocationSummaryRpt(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.getReports(
      "/agencyallocationgap/get/agencyAllocationSummaryRpt"
    );
  }

  generateAccountDashboard(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/accountdashboard/get/accountEsDashboardRpt",
      data
    );
  }

  downloadDashboardReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/accountdashboard/get/accountEsDownloadRpt",
      data
    );
  }

  // agent gap
  generateAgentGapAllocation(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/AgentAllocationGap/get/agentEsDashboardRpt",
      data
    );
  }

  downloadAgencyGapAllocation(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/AgencyAllocationGap/get/agencyEsDownloadRpt",
      data
    );
  }

  downloadAgentGapAllocation(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/AgentAllocationGap/get/agentEsDownloadRpt",
      data
    );
  }

  // performance
  generatePerformanceReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Performance/get/performanceReportWithHeatMapDashboard",
      data
    );
  }

  downloadPerformanceReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Performance/get/performanceEsDownloadRpt",
      data
    );
  }

  // target vs actual
  generateTargetvsActual(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.post("api/mvp/get/targetvsactualreport", data);
  }

  downloadTargetvsActual(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.post(
      "api/mvp/get/targetvsactualdownloadreport",
      data
    );
  }

  // superviosry
  generateSuperVisorReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Supervisory/get/supervisoryEsDashboardRpt",
      data
    );
  }

  downloadSuperVisorReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/Supervisory/get/supervisoryEsDownloadRpt",
      data
    );
  }

  generateACT(data) {
    return this.apiService.post("api/mvp/get/actreport", data);
  }

  getSupervisor(data) {
    return this.apiService.get("api/mvp/collectionstaff/byname/" + data);
  }

  getPaymentResolutions() {
    return this.apiService.get("api/mvp/paymentstatus");
  }

  getBaseBranches() {
    return this.apiService.get("api/mvp/get/basebranches");
  }

  getZones() {
    return this.apiService.get("api/mvp/get/zones");
  }

  getRegion() {
    return this.apiService.get("api/mvp/get/regions");
  }

  getStates() {
    return this.apiService.get("api/mvp/get/states");
  }

  getCities() {
    return this.apiService.get("api/mvp/get/cities");
  }

  getMasterCities(data) {
    return this.apiService.post("api/mvp/Master/City", data);
  }

  getMasterCountry() {
    return this.apiService.get("api/mvp/Master/Country");
  }

  regionReport(data) {
    return this.apiService.post("api/mvp/Master/Region", data);
  }

  stateReport(data) {
    return this.apiService.post("api/mvp/Master/State", data);
  }
  cityReport(data) {
    return this.apiService.post("api/mvp/Master/City", data);
  }

  branchReport(data) {
    return this.apiService.post("api/mvp/master/citybasedbranch", data);
  }

  saveFilterValues(data) {
    return this.apiService.post("api/mvp/add/FilterValues", data);
  }

  getSavedFilter(data) {
    return this.apiService.get("api/mvp/getfilterdetail/" + data);
  }

  getLegalCreatorList() {
    return this.apiService.get("api/mvp/GetCreatorName");
  }

  getDailyLegalReport(data) {
    let branchId = data.branchId ? data.branchId.toLowerCase() : "";
    if (branchId == "all") {
      data.branchId = null;
    }
    return this.apiService.post(
      "api/mvp/Legal/get/dailyLegaldashboardreport",
      data
    );
  }

  getdownloadCSVDataForDLReports(data) {
    let branchId = data.branchId ? data.branchId.toLowerCase() : "";
    if (branchId == "all") {
      data.branchId = null;
    }
    return this.apiService.post(
      "api/mvp/Legal/get/dailyLegaldownloadreport",
      data
    );
  }

  getDailyRepoReport(data) {
    return this.apiService.post(
      "api/mvp/get/dailyrepossessiondashboardreport",
      data
    );
  }

  getdownloadCSVDataForRepo(data) {
    return this.apiService.post(
      "api/mvp/get/dailyrepossessiondownloadreport",
      data
    );
  }

  getLegalworkflowStatusList() {
    return this.apiService.get("api/mvp/Legal/getAllWorkFlowStatus");
  }

  getRepoStatusList() {
    return this.apiService.get(
      "api/mvp/Repossession/GetAllRepossessionWorkFlowStatus"
    );
  }

  // not done sumathi
  generateAllocatedvsActual(data) {
    return this.apiService.postReports(
      "/AllocatedVsCollected/get/allocatedvsactualEsDashboardRpt",
      data
    );
  }

  downloadAllocatedvsCollected(data) {
    return this.apiService.postReports(
      "/AllocatedVsCollected/get/allocatedvsactualEsDownloadRpt",
      data
    );
  }

  downloadReportsRaw(fileName) {
    return this.apiService.getRawZip("/ReportsCommon/EsGetFileDd/" + fileName);
  }

  downloadtrailHistory(data) {
    return this.apiService.postReports(
      "/TrailHistory/get/trailHistoryEsDownloadRpt",
      data
    );
  }

  downloadCommuicatioHistory(data) {
    return this.apiService.postReports(
      "/CommunicationHistory/get/communicationHistoryEsDownloadRpt",
      data
    );
  }

  getAgencyList() {
    return this.apiService.get("api/mvp/agency");
  }

  getAgentList() {
    return this.apiService.get("api/mvp/agent/list");
  }

  getZoneList() {
    return this.apiService.get("api/mvp/get/zones");
  }

  getCollectionReport(data) {
    return this.apiService.post("api/mvp/get/collectionsdashboardreport", data);
  }

  downloadCCDReport(data) {
    let branchId = data.branch ? data.branch.toLowerCase() : "";
    if (branchId == "all") {
      data.branch = null;
    }
    return this.apiService.postReports(
      "/CCDReport/get/ccdreportEsDownloadRpt",
      data
    );
  }

  generateDetailedReport(data) {
    // let branchId = data.branch ? data.branch.toLowerCase() : "";
    // if (branchId == "all") {
    //   data.branch = null;
    // }
    return this.apiService.postReports(
      "/AgencyAllocationGap/get/agencyEsDetailDataRpt", data
    );
  }
  generateVisitIntensityReport(data) {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/VisitIntensity/get/visitIntensityEsDashboardRpt', data);

  }
  downloadVisitIntensityReport(data)
  {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/VisitIntensity/get/visitIntensityEsDownloadRpt', data);

  }
  // not done

  generateCollectionIntensityReport(data) {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/CollectionIntensity/get/collectionIntensityEsDashboardRpt', data);

  }
  downloadCollectionIntensityReport(data)
  {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/CollectionIntensity/get/collectionIntensityEsDownloadRpt', data);

  }

  downloadDailyTrailSummaryReport(data) {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/TrailSummary/get/TrailSummaryEsDownloadRpt', data);

  }
  compareDataCollectionTrend(data)
  {
    return this.apiService.postReports('/CollectionTrend/get/collectionTrendEsMonthDashboardRpt', data);

  }
  generateCollectionTrendReport(data) {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/CollectionTrend/get/collectionTrendEsDashboardRpt', data);

  }
  downloadCollectionTrendReport(data)
  {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/CollectionTrend/get/collectionTrendEsDownloadRpt', data);
  }

  getTeleAgencyName(){
    return this.apiService.get("api/mvp/TCAgency")
  }

  generateCashWalletLimitReport(data) {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/CashLimit/get/cashlimitEsDashboardRpt', data);

  }
  downloadCashWalletLimitReport(data)
  {
    let branchId = data.branch ? (data.branch).toLowerCase() : ''
    if(branchId=="all"){
      data.branch = null
    }
    return this.apiService.postReports('/CashLimit/get/cashlimitEsDownloadRpt', data);
  }


// newapis
  generateAgencyGap(data) {
    return this.apiService.postReports(
      "/AgencyAllocationGap/get/primaryallocationgap/dashboard",
      data
    );
  }

  downloadPrimaryAgency(data) {
    return this.apiService.postReports(
      "/AgencyAllocationGap/get/primaryallocationgap/download",
      data
    );
  }

  generateAgentGap(data) {
    return this.apiService.postReports(
      "/AgentAllocationGap/get/secondaryallocationgap/dashboard",
      data
    );
  }
  downloadSecondaryAgent(data) {
    return this.apiService.postReports(
      "/AgentAllocationGap/get/secondaryallocationgap/download",
      data
    );
  }

 
  generateAllocatedvsAchieved(data){
    return this.apiService.postReports(
      "/AllocatedVsCollected/get/allocationAchieved/dashboard",
      data
    );
  }

  downloadAllocatedvsAchieved(data) {
    return this.apiService.postReports(
      "/AllocatedVsCollected/get/allocationAchieved/download",
      data
    );
  }
}


