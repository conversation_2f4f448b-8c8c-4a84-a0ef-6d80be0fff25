{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./agency-allocation-gap.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./agency-allocation-gap.component.scss?ngResource\";\nimport { Component, ViewChild, inject } from \"@angular/core\";\nimport { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { CommonModule } from \"@angular/common\";\nimport { of } from \"rxjs\";\nimport * as d3 from \"d3\";\nimport { TypeaheadModule } from \"ngx-bootstrap/typeahead\";\nimport { BsDropdownModule } from \"ngx-bootstrap/dropdown\";\nimport { ACMP, AcmService } from \"src/app/shared\";\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\nimport { ReportService } from \"../../reports.service\";\nimport { ToastrService } from \"ngx-toastr\";\nlet AgencyAllocationGapComponent = class AgencyAllocationGapComponent {\n  constructor() {\n    this.acmService = inject(AcmService);\n    this.reportService = inject(ReportService);\n    this.toastr = inject(ToastrService);\n    this.noResultStaff = false;\n    this.data = null;\n    this.showChart = false;\n    this.breadcrumbData = [{\n      label: \"Reports\"\n    }, {\n      label: \"Allocation Reports\"\n    }, {\n      label: \"Agency Allocation Gap Report\"\n    }];\n    this.canDownloadReport = this.acmService.hasACMAccess([ACMP.CanDownloadAgencyAllocationGapReport]);\n    this.loader = {\n      generateReport: false,\n      downloadReport: false\n    };\n    this.ownerInputFormatter = item => item || \"\";\n  }\n  ngOnInit() {\n    this.searchForm = new FormGroup({\n      allocationOwners: new FormControl(\"\"),\n      staff: new FormControl(\"\")\n    });\n  }\n  ngAfterViewInit() {\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\n    this.searchForm.updateValueAndValidity();\n  }\n  getSupervisorList(term) {\n    if (!term || term.trim().length < 1) {\n      this.bankUserList = of([]);\n      return;\n    }\n    this.bankUserList = this.reportService.getSupervisor(term);\n  }\n  onSelectStaff(event) {\n    const display = `${event.item.firstName} - ${event.item.agencyCode}`;\n    this.searchForm.get(\"allocationOwners\")?.setValue(display);\n    this.selectedOwnerId = event.item.id;\n  }\n  ownerNameEmpty() {\n    this.searchForm.get(\"allocationOwners\")?.setValue(null);\n  }\n  typeaheadNoResultsStaff(event) {\n    this.noResultStaff = event;\n  }\n  typeaheadNoResults(event) {\n    if (event) {\n      this.toastr.info(\"Please enter correct supervising manager\", \"Info!\");\n    }\n  }\n  generatePayload() {\n    const fValue = this.searchForm?.value;\n    const obj = {};\n    const productLevels = Object.entries(fValue?.products || {}).filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0).map(([levelId, masterId]) => ({\n      levelId,\n      masterId\n    }));\n    if (productLevels.length) {\n      obj.products = {\n        levels: productLevels\n      };\n    }\n    const geoLevels = Object.entries(fValue?.geos || {}).filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0).map(([levelId, masterId]) => ({\n      levelId,\n      masterId\n    }));\n    if (geoLevels.length) {\n      obj.geos = {\n        levels: geoLevels\n      };\n    }\n    if (fValue?.buckets?.bucket?.length) {\n      obj.buckets = fValue.buckets.bucket;\n    }\n    if (this.selectedOwnerId) {\n      obj.allocationOwners = [this.selectedOwnerId];\n    }\n    const otherKeys = Object.keys(fValue || {}).filter(k => ![\"products\", \"geos\", \"buckets\", \"allocationOwners\"].includes(k));\n    otherKeys.forEach(k => {\n      if (fValue[k] !== null && fValue[k]?.length > 0) {\n        obj[k] = fValue[k];\n      }\n    });\n    return obj;\n  }\n  generateReport() {\n    const payload = this.generatePayload();\n    console.log(\"Payload:\", payload);\n    this.loader.generateReport = true;\n    this.reportService.generateAgencyGap(payload).subscribe({\n      next: response => {\n        this.loader.generateReport = false;\n        const {\n          allocatedCount,\n          unAllocatedCount\n        } = response;\n        if (!allocatedCount && !unAllocatedCount) {\n          this.toastr.info(\"No results found!\", \"Info!\");\n          this.showChart = false;\n          this.data = null;\n        } else {\n          this.data = response;\n          this.showChart = true;\n          setTimeout(() => {\n            this.createChart1();\n          }, 100);\n        }\n      },\n      error: err => {\n        this.toastr.error(err?.message || \"Something went wrong\", \"Error!\");\n        this.loader.generateReport = false;\n      }\n    });\n  }\n  createChart1() {\n    const chartData = {\n      title: \"Agency Allocated & Un-Allocated Accounts Pie Report\",\n      data: [{\n        trailStatus: \"Allocated\",\n        noOfAccounts: this.data?.allocatedCount ?? 0\n      }, {\n        trailStatus: \"Un-Allocated\",\n        noOfAccounts: this.data?.unAllocatedCount ?? 0\n      }]\n    };\n    const svg = d3.select(\"svg\");\n    svg.selectAll(\"*\").remove();\n    const width = 960;\n    const height = 500;\n    const chartWidth = 400;\n    const chartHeight = 400;\n    const radius = Math.min(chartWidth, chartHeight) / 2;\n    const colorScale = d3.scaleOrdinal().domain([\"Allocated\", \"Un-Allocated\"]).range([\"#27ae60\", \"#9b59b6\"]);\n    const arc = d3.arc().outerRadius(radius - 25).innerRadius(1.5);\n    const pie = d3.pie().sort(null).value(d => d.noOfAccounts).padAngle(0.01);\n    // Add title at the top\n    svg.append(\"text\").attr(\"x\", width / 2).attr(\"y\", 30).style(\"text-anchor\", \"middle\").style(\"font\", \"16px sans-serif\").style(\"font-weight\", \"bold\").text(chartData.title);\n    // Position chart on the left side\n    const arcGroup = svg.append(\"g\").attr(\"class\", \"arc-group\").attr(\"transform\", `translate(${chartWidth / 2 + 50}, ${height / 2})`);\n    const arcs = arcGroup.selectAll(\".arc\").data(pie(chartData.data)).enter().append(\"g\").attr(\"class\", \"arc\");\n    arcs.append(\"path\").attr(\"d\", arc).attr(\"fill\", d => colorScale(d.data.trailStatus)).on(\"mouseover\", (event, d) => {\n      d3.select(\"#tooltip\").style(\"display\", \"inline-block\").style(\"left\", event.pageX + \"px\").style(\"top\", event.pageY + \"px\").style(\"opacity\", 1).select(\"#value\").html(`<b>Status:</b> ${d.data.trailStatus}, <b>Value:</b> ${d.data.noOfAccounts}`);\n    }).on(\"mouseout\", () => {\n      d3.select(\"#tooltip\").style(\"display\", \"none\");\n    });\n    // Position legend on the right side, vertically centered\n    const legendBox = svg.append(\"g\").attr(\"class\", \"legend-group\").attr(\"transform\", `translate(${chartWidth + 150}, ${height / 2 - chartData.data.length * 25 / 2})`);\n    const legend = legendBox.selectAll(\".legend\").data(chartData.data).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (d, i) => `translate(0, ${i * 25})`);\n    legend.append(\"circle\").attr(\"r\", 8).attr(\"fill\", d => colorScale(d.trailStatus));\n    legend.append(\"text\").attr(\"x\", 15).attr(\"y\", 5).style(\"font\", \"14px sans-serif\").text(d => `${d.trailStatus} (${d.noOfAccounts})`);\n  }\n  downloadReport() {\n    // this.loader.isDownload = true;\n    const payload = this.generatePayload();\n    this.reportService.downloadPrimaryAgency(payload).subscribe(response => {\n      // this.loader.isDownload = false;\n      const fileName = response.filename;\n      this.getdata(fileName);\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      // this.loader.isDownload = false;\n    });\n  }\n  getdata(fileName) {\n    this.reportService.downloadFile(fileName).subscribe(response => {\n      if (response.length === 0) {\n        this.toastr.warning('No results found!', \"Warning!\");\n      } else {\n        const mediaType = 'application/zip';\n        const a = document.createElement(\"a\");\n        a.setAttribute('style', 'display:none;');\n        document.body.appendChild(a);\n        const blob = new Blob([response], {\n          type: mediaType\n        });\n        const url = window.URL.createObjectURL(blob);\n        a.href = url;\n        a.download = 'agencygap.zip';\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      }\n    });\n  }\n  static {\n    this.propDecorators = {\n      productHierarchy: [{\n        type: ViewChild,\n        args: [\"pr\"]\n      }],\n      bucketHierarchy: [{\n        type: ViewChild,\n        args: [\"buc\"]\n      }],\n      geoHierarchy: [{\n        type: ViewChild,\n        args: [\"geo\"]\n      }]\n    };\n  }\n};\nAgencyAllocationGapComponent = __decorate([Component({\n  selector: \"app-agency-allocation-gap\",\n  standalone: true,\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, BreadcrumbComponent, HierarchyFormDirective, HierarchyFormFieldComponent, TypeaheadModule, BsDropdownModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AgencyAllocationGapComponent);\nexport { AgencyAllocationGapComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "inject", "FormControl", "FormGroup", "FormsModule", "ReactiveFormsModule", "CommonModule", "of", "d3", "TypeaheadModule", "BsDropdownModule", "ACMP", "AcmService", "BreadcrumbComponent", "HierarchyFormFieldComponent", "HierarchyFormDirective", "ReportService", "ToastrService", "AgencyAllocationGapComponent", "constructor", "acmService", "reportService", "toastr", "noResultStaff", "data", "showChart", "breadcrumbData", "label", "canDownloadReport", "hasACMAccess", "CanDownloadAgencyAllocationGapReport", "loader", "generateReport", "downloadReport", "ownerInputFormatter", "item", "ngOnInit", "searchForm", "allocationOwners", "staff", "ngAfterViewInit", "addControl", "productHierarchy", "formGroup", "bucketHierarchy", "geoHierarchy", "updateValueAndValidity", "getSupervisorList", "term", "trim", "length", "bankUserList", "getSupervisor", "onSelectStaff", "event", "display", "firstName", "agencyCode", "get", "setValue", "selectedOwnerId", "id", "ownerNameEmpty", "typeaheadNoResultsStaff", "typeaheadNoResults", "info", "generatePayload", "fValue", "value", "obj", "productLevels", "Object", "entries", "products", "filter", "_", "masterId", "Array", "isArray", "map", "levelId", "levels", "geoLevels", "geos", "buckets", "bucket", "otherKeys", "keys", "k", "includes", "for<PERSON>ach", "payload", "console", "log", "generateAgencyGap", "subscribe", "next", "response", "allocatedCount", "unAllocatedCount", "setTimeout", "createChart1", "error", "err", "message", "chartData", "title", "trailStatus", "noOfAccounts", "svg", "select", "selectAll", "remove", "width", "height", "chartWidth", "chartHeight", "radius", "Math", "min", "colorScale", "scaleOrdinal", "domain", "range", "arc", "outerRadius", "innerRadius", "pie", "sort", "d", "padAngle", "append", "attr", "style", "text", "arcGroup", "arcs", "enter", "on", "pageX", "pageY", "html", "legendBox", "legend", "i", "downloadPrimaryAgency", "fileName", "filename", "getdata", "downloadFile", "warning", "mediaType", "a", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "href", "download", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\github\\sowreports\\SCB-ENCollect.FE.Sleek\\src\\app\\reports\\pages\\agency-allocation-gap\\agency-allocation-gap.component.ts"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  Component,\r\n  ViewChild,\r\n  OnInit,\r\n  inject,\r\n} from \"@angular/core\";\r\nimport {\r\n  FormControl,\r\n  FormGroup,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from \"@angular/forms\";\r\nimport { CommonModule } from \"@angular/common\";\r\nimport { Observable, of } from \"rxjs\";\r\nimport { map } from \"rxjs/operators\";\r\nimport * as d3 from \"d3\";\r\nimport { TypeaheadModule } from \"ngx-bootstrap/typeahead\";\r\nimport { BsDropdownModule } from \"ngx-bootstrap/dropdown\";\r\n\r\nimport { ACMP, AcmService } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\nimport { HierarchyFormFieldComponent } from \"src/app/shared/components/hierarchy-form-field/hierarchy-form-field.component\";\r\nimport { HierarchyFormDirective } from \"src/app/shared/directives/hierarchy-form.directive\";\r\nimport { ReportService } from \"../../reports.service\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\n\r\n@Component({\r\n  selector: \"app-agency-allocation-gap\",\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    BreadcrumbComponent,\r\n    HierarchyFormDirective,\r\n    HierarchyFormFieldComponent,\r\n    TypeaheadModule,\r\n    BsDropdownModule,\r\n  ],\r\n  templateUrl: \"./agency-allocation-gap.component.html\",\r\n  styleUrl: \"./agency-allocation-gap.component.scss\",\r\n})\r\nexport class AgencyAllocationGapComponent implements OnInit, AfterViewInit {\r\n  private acmService = inject(AcmService);\r\n  private reportService = inject(ReportService);\r\n  private toastr = inject(ToastrService);\r\n\r\n  @ViewChild(\"pr\") productHierarchy!: HierarchyFormDirective;\r\n  @ViewChild(\"buc\") bucketHierarchy!: HierarchyFormDirective;\r\n  @ViewChild(\"geo\") geoHierarchy!: HierarchyFormDirective;\r\n\r\n  searchForm!: FormGroup;\r\n  bankUserList!: Observable<any[]>;\r\n  noResultStaff = false;\r\n  data: any = null;\r\n  showChart = false;\r\n  selectedOwnerId: any;\r\n\r\n  breadcrumbData = [\r\n    { label: \"Reports\" },\r\n    { label: \"Allocation Reports\" },\r\n    { label: \"Agency Allocation Gap Report\" },\r\n  ];\r\n\r\n  canDownloadReport = this.acmService.hasACMAccess([\r\n    ACMP.CanDownloadAgencyAllocationGapReport,\r\n  ]);\r\n\r\n  loader = {\r\n    generateReport: false,\r\n    downloadReport: false,\r\n  };\r\n\r\n  ngOnInit(): void {\r\n    this.searchForm = new FormGroup({\r\n      allocationOwners: new FormControl(\"\"), \r\n      staff: new FormControl(\"\"),\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.searchForm.addControl(\"products\", this.productHierarchy.formGroup);\r\n    this.searchForm.addControl(\"buckets\", this.bucketHierarchy.formGroup);\r\n    this.searchForm.addControl(\"geos\", this.geoHierarchy.formGroup);\r\n    this.searchForm.updateValueAndValidity();\r\n  }\r\n\r\n  getSupervisorList(term: string): void {\r\n    if (!term || term.trim().length < 1) {\r\n      this.bankUserList = of([]);\r\n      return;\r\n    }\r\n    this.bankUserList = this.reportService.getSupervisor(term);\r\n  }\r\n\r\n  onSelectStaff(event: any): void {\r\n    const display = `${event.item.firstName} - ${event.item.agencyCode}`;\r\n    this.searchForm.get(\"allocationOwners\")?.setValue(display);\r\n    this.selectedOwnerId = event.item.id;\r\n  }\r\n\r\n  ownerNameEmpty(): void {\r\n    this.searchForm.get(\"allocationOwners\")?.setValue(null);\r\n  }\r\n\r\n  typeaheadNoResultsStaff(event: boolean): void {\r\n    this.noResultStaff = event;\r\n  }\r\n\r\n  typeaheadNoResults(event: boolean): void {\r\n    if (event) {\r\n      this.toastr.info(\"Please enter correct supervising manager\", \"Info!\");\r\n    }\r\n  }\r\n\r\n  ownerInputFormatter = (item: any): string => item || \"\";\r\n\r\n  generatePayload() {\r\n    const fValue = this.searchForm?.value;\r\n    const obj: any = {};\r\n\r\n    const productLevels = Object.entries(fValue?.products || {})\r\n      .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)\r\n      .map(([levelId, masterId]) => ({ levelId, masterId }));\r\n    if (productLevels.length) {\r\n      obj.products = { levels: productLevels };\r\n    }\r\n\r\n    const geoLevels = Object.entries(fValue?.geos || {})\r\n      .filter(([_, masterId]) => Array.isArray(masterId) && masterId.length > 0)\r\n      .map(([levelId, masterId]) => ({ levelId, masterId }));\r\n    if (geoLevels.length) {\r\n      obj.geos = { levels: geoLevels };\r\n    }\r\n\r\n    if (fValue?.buckets?.bucket?.length) {\r\n      obj.buckets = fValue.buckets.bucket;\r\n    }\r\n\r\n    if (this.selectedOwnerId) {\r\n      obj.allocationOwners = [this.selectedOwnerId];\r\n    }\r\n\r\n    const otherKeys = Object.keys(fValue || {}).filter(\r\n      (k) => ![\"products\", \"geos\", \"buckets\", \"allocationOwners\"].includes(k)\r\n    );\r\n\r\n    otherKeys.forEach((k) => {\r\n      if (fValue[k] !== null && fValue[k]?.length > 0) {\r\n        obj[k] = fValue[k];\r\n      }\r\n    });\r\n\r\n    return obj;\r\n  }\r\n\r\n  generateReport(): void {\r\n    const payload = this.generatePayload();\r\n    console.log(\"Payload:\", payload);\r\n    this.loader.generateReport = true;\r\n\r\n    this.reportService.generateAgencyGap(payload).subscribe({\r\n      next: (response) => {\r\n        this.loader.generateReport = false;\r\n        const { allocatedCount, unAllocatedCount } = response;\r\n\r\n        if (!allocatedCount && !unAllocatedCount) {\r\n          this.toastr.info(\"No results found!\", \"Info!\");\r\n          this.showChart = false;\r\n          this.data = null;\r\n        } else {\r\n          this.data = response;\r\n          this.showChart = true;\r\n          setTimeout(() => {\r\n            this.createChart1();\r\n          }, 100);\r\n        }\r\n      },\r\n      error: (err) => {\r\n        this.toastr.error(err?.message || \"Something went wrong\", \"Error!\");\r\n        this.loader.generateReport = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  createChart1(): void {\r\n    const chartData = {\r\n      title: \"Agency Allocated & Un-Allocated Accounts Pie Report\",\r\n      data: [\r\n        {\r\n          trailStatus: \"Allocated\",\r\n          noOfAccounts: this.data?.allocatedCount ?? 0,\r\n        },\r\n        {\r\n          trailStatus: \"Un-Allocated\",\r\n          noOfAccounts: this.data?.unAllocatedCount ?? 0,\r\n        },\r\n      ],\r\n    };\r\n\r\n    const svg = d3.select(\"svg\");\r\n    svg.selectAll(\"*\").remove();\r\n\r\n    const width = 960;\r\n    const height = 500;\r\n    const chartWidth = 400;\r\n    const chartHeight = 400;\r\n    const radius = Math.min(chartWidth, chartHeight) / 2;\r\n\r\n    const colorScale = d3\r\n      .scaleOrdinal<string>()\r\n      .domain([\"Allocated\", \"Un-Allocated\"])\r\n      .range([\"#27ae60\", \"#9b59b6\"]);\r\n\r\n    const arc = d3.arc<any>().outerRadius(radius - 25).innerRadius(1.5);\r\n\r\n    const pie = d3\r\n      .pie<any>()\r\n      .sort(null)\r\n      .value((d) => d.noOfAccounts)\r\n      .padAngle(0.01);\r\n\r\n    // Add title at the top\r\n    svg\r\n      .append(\"text\")\r\n      .attr(\"x\", width / 2)\r\n      .attr(\"y\", 30)\r\n      .style(\"text-anchor\", \"middle\")\r\n      .style(\"font\", \"16px sans-serif\")\r\n      .style(\"font-weight\", \"bold\")\r\n      .text(chartData.title);\r\n\r\n    // Position chart on the left side\r\n    const arcGroup = svg\r\n      .append(\"g\")\r\n      .attr(\"class\", \"arc-group\")\r\n      .attr(\"transform\", `translate(${chartWidth / 2 + 50}, ${height / 2})`);\r\n\r\n    const arcs = arcGroup\r\n      .selectAll(\".arc\")\r\n      .data(pie(chartData.data))\r\n      .enter()\r\n      .append(\"g\")\r\n      .attr(\"class\", \"arc\");\r\n\r\n    arcs\r\n      .append(\"path\")\r\n      .attr(\"d\", <any>arc)\r\n      .attr(\"fill\", (d) => colorScale(d.data.trailStatus))\r\n      .on(\"mouseover\", (event, d) => {\r\n        d3.select(\"#tooltip\")\r\n          .style(\"display\", \"inline-block\")\r\n          .style(\"left\", event.pageX + \"px\")\r\n          .style(\"top\", event.pageY + \"px\")\r\n          .style(\"opacity\", 1)\r\n          .select(\"#value\")\r\n          .html(\r\n            `<b>Status:</b> ${d.data.trailStatus}, <b>Value:</b> ${d.data.noOfAccounts}`\r\n          );\r\n      })\r\n      .on(\"mouseout\", () => {\r\n        d3.select(\"#tooltip\").style(\"display\", \"none\");\r\n      });\r\n\r\n    // Position legend on the right side, vertically centered\r\n    const legendBox = svg\r\n      .append(\"g\")\r\n      .attr(\"class\", \"legend-group\")\r\n      .attr(\"transform\", `translate(${chartWidth + 150}, ${height / 2 - (chartData.data.length * 25) / 2})`);\r\n\r\n    const legend = legendBox\r\n      .selectAll(\".legend\")\r\n      .data(chartData.data)\r\n      .enter()\r\n      .append(\"g\")\r\n      .attr(\"class\", \"legend\")\r\n      .attr(\"transform\", (d, i) => `translate(0, ${i * 25})`);\r\n\r\n    legend\r\n      .append(\"circle\")\r\n      .attr(\"r\", 8)\r\n      .attr(\"fill\", (d) => colorScale(d.trailStatus));\r\n\r\n    legend\r\n      .append(\"text\")\r\n      .attr(\"x\", 15)\r\n      .attr(\"y\", 5)\r\n      .style(\"font\", \"14px sans-serif\")\r\n      .text((d) => `${d.trailStatus} (${d.noOfAccounts})`);\r\n  }\r\n\r\n  downloadReport() {\r\n   // this.loader.isDownload = true;\r\n  const payload = this.generatePayload();\r\n  this.reportService.downloadPrimaryAgency(payload).subscribe(\r\n    response => {\r\n      // this.loader.isDownload = false;\r\n      const fileName = response.filename;\r\n      this.getdata(fileName);\r\n    },\r\n    err => {\r\n      this.toastr.error(err, \"Error!\");\r\n      // this.loader.isDownload = false;\r\n    }\r\n  );\r\n}\r\n\r\ngetdata(fileName: string) {\r\n  this.reportService.downloadFile(fileName).subscribe(response => {\r\n    if (response.length === 0) {\r\n      this.toastr.warning('No results found!', \"Warning!\");\r\n    } else {\r\n      const mediaType = 'application/zip';\r\n      const a = document.createElement(\"a\");\r\n      a.setAttribute('style', 'display:none;');\r\n      document.body.appendChild(a);\r\n      const blob = new Blob([response], { type: mediaType });\r\n      const url = window.URL.createObjectURL(blob);\r\n      a.href = url;\r\n      a.download = 'agencygap.zip';\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n      document.body.removeChild(a);\r\n    }\r\n  });\r\n}\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAEEA,SAAS,EACTC,SAAS,EAETC,MAAM,QACD,eAAe;AACtB,SACEC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAqBC,EAAE,QAAQ,MAAM;AAErC,OAAO,KAAKC,EAAE,MAAM,IAAI;AACxB,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,gBAAgB,QAAQ,wBAAwB;AAEzD,SAASC,IAAI,EAAEC,UAAU,QAAQ,gBAAgB;AACjD,SAASC,mBAAmB,QAAQ,2DAA2D;AAC/F,SAASC,2BAA2B,QAAQ,+EAA+E;AAC3H,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,aAAa,QAAQ,YAAY;AAkBnC,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA4B;EAAlCC,YAAA;IACG,KAAAC,UAAU,GAAGnB,MAAM,CAACW,UAAU,CAAC;IAC/B,KAAAS,aAAa,GAAGpB,MAAM,CAACe,aAAa,CAAC;IACrC,KAAAM,MAAM,GAAGrB,MAAM,CAACgB,aAAa,CAAC;IAQtC,KAAAM,aAAa,GAAG,KAAK;IACrB,KAAAC,IAAI,GAAQ,IAAI;IAChB,KAAAC,SAAS,GAAG,KAAK;IAGjB,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAS,CAAE,EACpB;MAAEA,KAAK,EAAE;IAAoB,CAAE,EAC/B;MAAEA,KAAK,EAAE;IAA8B,CAAE,CAC1C;IAED,KAAAC,iBAAiB,GAAG,IAAI,CAACR,UAAU,CAACS,YAAY,CAAC,CAC/ClB,IAAI,CAACmB,oCAAoC,CAC1C,CAAC;IAEF,KAAAC,MAAM,GAAG;MACPC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE;KACjB;IA4CD,KAAAC,mBAAmB,GAAIC,IAAS,IAAaA,IAAI,IAAI,EAAE;EAoNzD;EA9PEC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,GAAG,IAAIlC,SAAS,CAAC;MAC9BmC,gBAAgB,EAAE,IAAIpC,WAAW,CAAC,EAAE,CAAC;MACrCqC,KAAK,EAAE,IAAIrC,WAAW,CAAC,EAAE;KAC1B,CAAC;EACJ;EAEAsC,eAAeA,CAAA;IACb,IAAI,CAACH,UAAU,CAACI,UAAU,CAAC,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAACC,SAAS,CAAC;IACvE,IAAI,CAACN,UAAU,CAACI,UAAU,CAAC,SAAS,EAAE,IAAI,CAACG,eAAe,CAACD,SAAS,CAAC;IACrE,IAAI,CAACN,UAAU,CAACI,UAAU,CAAC,MAAM,EAAE,IAAI,CAACI,YAAY,CAACF,SAAS,CAAC;IAC/D,IAAI,CAACN,UAAU,CAACS,sBAAsB,EAAE;EAC1C;EAEAC,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAACC,YAAY,GAAG5C,EAAE,CAAC,EAAE,CAAC;MAC1B;IACF;IACA,IAAI,CAAC4C,YAAY,GAAG,IAAI,CAAC9B,aAAa,CAAC+B,aAAa,CAACJ,IAAI,CAAC;EAC5D;EAEAK,aAAaA,CAACC,KAAU;IACtB,MAAMC,OAAO,GAAG,GAAGD,KAAK,CAACnB,IAAI,CAACqB,SAAS,MAAMF,KAAK,CAACnB,IAAI,CAACsB,UAAU,EAAE;IACpE,IAAI,CAACpB,UAAU,CAACqB,GAAG,CAAC,kBAAkB,CAAC,EAAEC,QAAQ,CAACJ,OAAO,CAAC;IAC1D,IAAI,CAACK,eAAe,GAAGN,KAAK,CAACnB,IAAI,CAAC0B,EAAE;EACtC;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACzB,UAAU,CAACqB,GAAG,CAAC,kBAAkB,CAAC,EAAEC,QAAQ,CAAC,IAAI,CAAC;EACzD;EAEAI,uBAAuBA,CAACT,KAAc;IACpC,IAAI,CAAC/B,aAAa,GAAG+B,KAAK;EAC5B;EAEAU,kBAAkBA,CAACV,KAAc;IAC/B,IAAIA,KAAK,EAAE;MACT,IAAI,CAAChC,MAAM,CAAC2C,IAAI,CAAC,0CAA0C,EAAE,OAAO,CAAC;IACvE;EACF;EAIAC,eAAeA,CAAA;IACb,MAAMC,MAAM,GAAG,IAAI,CAAC9B,UAAU,EAAE+B,KAAK;IACrC,MAAMC,GAAG,GAAQ,EAAE;IAEnB,MAAMC,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACL,MAAM,EAAEM,QAAQ,IAAI,EAAE,CAAC,CACzDC,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,QAAQ,CAAC,KAAKC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,IAAIA,QAAQ,CAAC1B,MAAM,GAAG,CAAC,CAAC,CACzE6B,GAAG,CAAC,CAAC,CAACC,OAAO,EAAEJ,QAAQ,CAAC,MAAM;MAAEI,OAAO;MAAEJ;IAAQ,CAAE,CAAC,CAAC;IACxD,IAAIN,aAAa,CAACpB,MAAM,EAAE;MACxBmB,GAAG,CAACI,QAAQ,GAAG;QAAEQ,MAAM,EAAEX;MAAa,CAAE;IAC1C;IAEA,MAAMY,SAAS,GAAGX,MAAM,CAACC,OAAO,CAACL,MAAM,EAAEgB,IAAI,IAAI,EAAE,CAAC,CACjDT,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,QAAQ,CAAC,KAAKC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,IAAIA,QAAQ,CAAC1B,MAAM,GAAG,CAAC,CAAC,CACzE6B,GAAG,CAAC,CAAC,CAACC,OAAO,EAAEJ,QAAQ,CAAC,MAAM;MAAEI,OAAO;MAAEJ;IAAQ,CAAE,CAAC,CAAC;IACxD,IAAIM,SAAS,CAAChC,MAAM,EAAE;MACpBmB,GAAG,CAACc,IAAI,GAAG;QAAEF,MAAM,EAAEC;MAAS,CAAE;IAClC;IAEA,IAAIf,MAAM,EAAEiB,OAAO,EAAEC,MAAM,EAAEnC,MAAM,EAAE;MACnCmB,GAAG,CAACe,OAAO,GAAGjB,MAAM,CAACiB,OAAO,CAACC,MAAM;IACrC;IAEA,IAAI,IAAI,CAACzB,eAAe,EAAE;MACxBS,GAAG,CAAC/B,gBAAgB,GAAG,CAAC,IAAI,CAACsB,eAAe,CAAC;IAC/C;IAEA,MAAM0B,SAAS,GAAGf,MAAM,CAACgB,IAAI,CAACpB,MAAM,IAAI,EAAE,CAAC,CAACO,MAAM,CAC/Cc,CAAC,IAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAACC,QAAQ,CAACD,CAAC,CAAC,CACxE;IAEDF,SAAS,CAACI,OAAO,CAAEF,CAAC,IAAI;MACtB,IAAIrB,MAAM,CAACqB,CAAC,CAAC,KAAK,IAAI,IAAIrB,MAAM,CAACqB,CAAC,CAAC,EAAEtC,MAAM,GAAG,CAAC,EAAE;QAC/CmB,GAAG,CAACmB,CAAC,CAAC,GAAGrB,MAAM,CAACqB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAOnB,GAAG;EACZ;EAEArC,cAAcA,CAAA;IACZ,MAAM2D,OAAO,GAAG,IAAI,CAACzB,eAAe,EAAE;IACtC0B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,OAAO,CAAC;IAChC,IAAI,CAAC5D,MAAM,CAACC,cAAc,GAAG,IAAI;IAEjC,IAAI,CAACX,aAAa,CAACyE,iBAAiB,CAACH,OAAO,CAAC,CAACI,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClE,MAAM,CAACC,cAAc,GAAG,KAAK;QAClC,MAAM;UAAEkE,cAAc;UAAEC;QAAgB,CAAE,GAAGF,QAAQ;QAErD,IAAI,CAACC,cAAc,IAAI,CAACC,gBAAgB,EAAE;UACxC,IAAI,CAAC7E,MAAM,CAAC2C,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC;UAC9C,IAAI,CAACxC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,IAAI,GAAG,IAAI;QAClB,CAAC,MAAM;UACL,IAAI,CAACA,IAAI,GAAGyE,QAAQ;UACpB,IAAI,CAACxE,SAAS,GAAG,IAAI;UACrB2E,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,YAAY,EAAE;UACrB,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACjF,MAAM,CAACgF,KAAK,CAACC,GAAG,EAAEC,OAAO,IAAI,sBAAsB,EAAE,QAAQ,CAAC;QACnE,IAAI,CAACzE,MAAM,CAACC,cAAc,GAAG,KAAK;MACpC;KACD,CAAC;EACJ;EAEAqE,YAAYA,CAAA;IACV,MAAMI,SAAS,GAAG;MAChBC,KAAK,EAAE,qDAAqD;MAC5DlF,IAAI,EAAE,CACJ;QACEmF,WAAW,EAAE,WAAW;QACxBC,YAAY,EAAE,IAAI,CAACpF,IAAI,EAAE0E,cAAc,IAAI;OAC5C,EACD;QACES,WAAW,EAAE,cAAc;QAC3BC,YAAY,EAAE,IAAI,CAACpF,IAAI,EAAE2E,gBAAgB,IAAI;OAC9C;KAEJ;IAED,MAAMU,GAAG,GAAGrG,EAAE,CAACsG,MAAM,CAAC,KAAK,CAAC;IAC5BD,GAAG,CAACE,SAAS,CAAC,GAAG,CAAC,CAACC,MAAM,EAAE;IAE3B,MAAMC,KAAK,GAAG,GAAG;IACjB,MAAMC,MAAM,GAAG,GAAG;IAClB,MAAMC,UAAU,GAAG,GAAG;IACtB,MAAMC,WAAW,GAAG,GAAG;IACvB,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,UAAU,EAAEC,WAAW,CAAC,GAAG,CAAC;IAEpD,MAAMI,UAAU,GAAGhH,EAAE,CAClBiH,YAAY,EAAU,CACtBC,MAAM,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CACrCC,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAEhC,MAAMC,GAAG,GAAGpH,EAAE,CAACoH,GAAG,EAAO,CAACC,WAAW,CAACR,MAAM,GAAG,EAAE,CAAC,CAACS,WAAW,CAAC,GAAG,CAAC;IAEnE,MAAMC,GAAG,GAAGvH,EAAE,CACXuH,GAAG,EAAO,CACVC,IAAI,CAAC,IAAI,CAAC,CACV5D,KAAK,CAAE6D,CAAC,IAAKA,CAAC,CAACrB,YAAY,CAAC,CAC5BsB,QAAQ,CAAC,IAAI,CAAC;IAEjB;IACArB,GAAG,CACAsB,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,GAAG,EAAEnB,KAAK,GAAG,CAAC,CAAC,CACpBmB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CACbC,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC9BA,KAAK,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAChCA,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,CAC5BC,IAAI,CAAC7B,SAAS,CAACC,KAAK,CAAC;IAExB;IACA,MAAM6B,QAAQ,GAAG1B,GAAG,CACjBsB,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAC1BA,IAAI,CAAC,WAAW,EAAE,aAAajB,UAAU,GAAG,CAAC,GAAG,EAAE,KAAKD,MAAM,GAAG,CAAC,GAAG,CAAC;IAExE,MAAMsB,IAAI,GAAGD,QAAQ,CAClBxB,SAAS,CAAC,MAAM,CAAC,CACjBvF,IAAI,CAACuG,GAAG,CAACtB,SAAS,CAACjF,IAAI,CAAC,CAAC,CACzBiH,KAAK,EAAE,CACPN,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;IAEvBI,IAAI,CACDL,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,GAAG,EAAOR,GAAG,CAAC,CACnBQ,IAAI,CAAC,MAAM,EAAGH,CAAC,IAAKT,UAAU,CAACS,CAAC,CAACzG,IAAI,CAACmF,WAAW,CAAC,CAAC,CACnD+B,EAAE,CAAC,WAAW,EAAE,CAACpF,KAAK,EAAE2E,CAAC,KAAI;MAC5BzH,EAAE,CAACsG,MAAM,CAAC,UAAU,CAAC,CAClBuB,KAAK,CAAC,SAAS,EAAE,cAAc,CAAC,CAChCA,KAAK,CAAC,MAAM,EAAE/E,KAAK,CAACqF,KAAK,GAAG,IAAI,CAAC,CACjCN,KAAK,CAAC,KAAK,EAAE/E,KAAK,CAACsF,KAAK,GAAG,IAAI,CAAC,CAChCP,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBvB,MAAM,CAAC,QAAQ,CAAC,CAChB+B,IAAI,CACH,kBAAkBZ,CAAC,CAACzG,IAAI,CAACmF,WAAW,mBAAmBsB,CAAC,CAACzG,IAAI,CAACoF,YAAY,EAAE,CAC7E;IACL,CAAC,CAAC,CACD8B,EAAE,CAAC,UAAU,EAAE,MAAK;MACnBlI,EAAE,CAACsG,MAAM,CAAC,UAAU,CAAC,CAACuB,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC;IAChD,CAAC,CAAC;IAEJ;IACA,MAAMS,SAAS,GAAGjC,GAAG,CAClBsB,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAC7BA,IAAI,CAAC,WAAW,EAAE,aAAajB,UAAU,GAAG,GAAG,KAAKD,MAAM,GAAG,CAAC,GAAIT,SAAS,CAACjF,IAAI,CAAC0B,MAAM,GAAG,EAAE,GAAI,CAAC,GAAG,CAAC;IAExG,MAAM6F,MAAM,GAAGD,SAAS,CACrB/B,SAAS,CAAC,SAAS,CAAC,CACpBvF,IAAI,CAACiF,SAAS,CAACjF,IAAI,CAAC,CACpBiH,KAAK,EAAE,CACPN,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CACvBA,IAAI,CAAC,WAAW,EAAE,CAACH,CAAC,EAAEe,CAAC,KAAK,gBAAgBA,CAAC,GAAG,EAAE,GAAG,CAAC;IAEzDD,MAAM,CACHZ,MAAM,CAAC,QAAQ,CAAC,CAChBC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CACZA,IAAI,CAAC,MAAM,EAAGH,CAAC,IAAKT,UAAU,CAACS,CAAC,CAACtB,WAAW,CAAC,CAAC;IAEjDoC,MAAM,CACHZ,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CACbA,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CACZC,KAAK,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAChCC,IAAI,CAAEL,CAAC,IAAK,GAAGA,CAAC,CAACtB,WAAW,KAAKsB,CAAC,CAACrB,YAAY,GAAG,CAAC;EACxD;EAEA3E,cAAcA,CAAA;IACb;IACD,MAAM0D,OAAO,GAAG,IAAI,CAACzB,eAAe,EAAE;IACtC,IAAI,CAAC7C,aAAa,CAAC4H,qBAAqB,CAACtD,OAAO,CAAC,CAACI,SAAS,CACzDE,QAAQ,IAAG;MACT;MACA,MAAMiD,QAAQ,GAAGjD,QAAQ,CAACkD,QAAQ;MAClC,IAAI,CAACC,OAAO,CAACF,QAAQ,CAAC;IACxB,CAAC,EACD3C,GAAG,IAAG;MACJ,IAAI,CAACjF,MAAM,CAACgF,KAAK,CAACC,GAAG,EAAE,QAAQ,CAAC;MAChC;IACF,CAAC,CACF;EACH;EAEA6C,OAAOA,CAACF,QAAgB;IACtB,IAAI,CAAC7H,aAAa,CAACgI,YAAY,CAACH,QAAQ,CAAC,CAACnD,SAAS,CAACE,QAAQ,IAAG;MAC7D,IAAIA,QAAQ,CAAC/C,MAAM,KAAK,CAAC,EAAE;QACzB,IAAI,CAAC5B,MAAM,CAACgI,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC;MACtD,CAAC,MAAM;QACL,MAAMC,SAAS,GAAG,iBAAiB;QACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACxCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,CAAC,CAAC;QAC5B,MAAMM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC9D,QAAQ,CAAC,EAAE;UAAE+D,IAAI,EAAET;QAAS,CAAE,CAAC;QACtD,MAAMU,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QAC5CN,CAAC,CAACa,IAAI,GAAGJ,GAAG;QACZT,CAAC,CAACc,QAAQ,GAAG,eAAe;QAC5Bd,CAAC,CAACe,KAAK,EAAE;QACTL,MAAM,CAACC,GAAG,CAACK,eAAe,CAACP,GAAG,CAAC;QAC/BR,QAAQ,CAACG,IAAI,CAACa,WAAW,CAACjB,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;;;;cAtRGxJ,SAAS;QAAA0K,IAAA,GAAC,IAAI;MAAA;;cACd1K,SAAS;QAAA0K,IAAA,GAAC,KAAK;MAAA;;cACf1K,SAAS;QAAA0K,IAAA,GAAC,KAAK;MAAA;;;;AAPLxJ,4BAA4B,GAAAyJ,UAAA,EAhBxC5K,SAAS,CAAC;EACT6K,QAAQ,EAAE,2BAA2B;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPxK,YAAY,EACZF,WAAW,EACXC,mBAAmB,EACnBQ,mBAAmB,EACnBE,sBAAsB,EACtBD,2BAA2B,EAC3BL,eAAe,EACfC,gBAAgB,CACjB;EACDqK,QAAA,EAAAC,oBAAqD;;CAEtD,CAAC,C,EACW9J,4BAA4B,CA6RxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}