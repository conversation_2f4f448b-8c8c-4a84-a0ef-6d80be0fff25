{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./menu-layout.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./menu-layout.component.scss?ngResource\";\nimport { Component, inject } from \"@angular/core\";\nimport { NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router, RouterLink, RouterLinkActive } from \"@angular/router\";\nimport { interval, map } from \"rxjs\";\nimport { UserService } from \"src/app/authentication/user.service\";\nimport { ACMP, AcmService } from \"../../services\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { JWTTokenService } from \"../../services/jwt-token.service\";\nimport { AsyncPipe, DatePipe, NgClass, NgTemplateOutlet } from \"@angular/common\";\nimport { NgScrollbarModule } from \"ngx-scrollbar\";\nimport { BsDropdownModule } from \"ngx-bootstrap/dropdown\";\nconst leftMenuMode = localStorage.getItem(\"leftMenuMode\") || \"push\";\nlet MenuLayoutComponent = class MenuLayoutComponent {\n  constructor(router, userService, acmService, toastr) {\n    this.router = router;\n    this.userService = userService;\n    this.acmService = acmService;\n    this.toastr = toastr;\n    this.jwtTokenService = inject(JWTTokenService);\n    this.currentTime$ = interval(1000).pipe(map(() => new Date()));\n    this.isLoading = false;\n    this.isSideNavExpanded = leftMenuMode === \"push\";\n    this.leftMenuMode = leftMenuMode;\n    this.profileImage = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOYAAADmCAAAAADkAaZnAAAFCElEQVR42u3cV7LbPAwFYO1/i6C61ahiq1j2zV+Sh8xkEsdVBGyA5FkBviHFIlIKvjuRwDM90zM984PM/52IZ3qmZ3qmZ3qmZ3qmZ9rM/M+JeKZneqZneqZneqZnOsr814l4pmd6pmd6pmd6pmd6ps3Mf5yIZ3qmZ3qmU8zZAeaxjeFn8mp/tJH5NbdFCH8kLPqTVcxTk8L1xNX4ZQnzmMPd5OSNGnwjz1rB48RNm/YrWQ30TK3g6WSTUOaUwEvJzhKZGl6NWsQxlwSAh5OSOSjYEnUWxaxhY3aCmOcSNmcSwzwn25WQrUKYRkqA6CyCaagEAKUFMI2VACUu84sgJSAkmREromBqwEnPmjkCVmrGzJNCY0LLl5kBYvZcmT2mEtSJJxOzywIA7HgyS0DOyJF5xFZCxpGJ3pgAMz8mfmMiTZ4B98YEhcI8I2YBikwIlaEyGxJmw40ZkTAzZsyJRAmKGbOgYcLKixkRMQ+smER9lhuzpWI2rJiZG0xwgjmxZp6w0tExzYvDYzZuMMlGIBjcYB7cYI5uME9OMCM3mBkrZsF4PpEwbw6smJqKecRgrljZEylzjOLwmAsRs+fFXGnekaiVGTMnYZbcmDRj0IEbk2RfnazcmGtCwNT8mBSv9hZ+zIXppInMXJlOmrjMpWA6aaIyDxHbcXZdgyNWUpKVHlJxaMyRZHEQcWMORLswJ5iKG3MmYabcmEeSfVjDjrkjeRPNjklxIhYf2TEpem3DkElwYW9gyKzxma0bzBqNuaCFYKitsGpDZKaeaRGToNM2DJkHgu0mQyZBcy4cmRPyF0WQsWQuFTJT82QiN6daeDIXzXM6wWYumLuUGLGuYEZNH2MZs2pCrAuZOc89yv2gDLkqdOY8Y3Tcij8T4yil5s/EmD17N5iDG8zZCWbsBjN1g1kJYCL8x0KjMyf0IBzoDtg1ETAn8+XeJIFpfE86FsE8mO6ucxHMyfTKaSODaTrW9jKYk9nmWk1CmGYrhEQK0+wrhlIKcwpNmJ0YZsLr0aRimjycGQVzJInJ6VhDUA8RczS4VjIIYu43L/iSURBz3HqcojpRzHHjYFuNspg7Pl2WkLnx3/WVLOZ+6+q9FMXcvuXMBTFNNta5GKbZ64NaBnNv+oWjxmce0NOFhkpQHXZN+EyMU1x0JzZT49wlUZozc8D7/8pu4MocCsxbXmHNkomLRIYGbJEAAGExcGL2ZP9EUkXPhalToAzGYGTOrCMgjjLvuoHpIxnCG6IKU+beIP1OwZsS1iaF7g2YXQbvTFi0H2DqBN6fKCkqrfU7mF1dJCF8OCrJypaOWecfF/5mTauegNnlCrglrZGZH3kYnxmaKkQmV+ST0OeYXQqsE2oMZqmAe9LelNklICBKmzG1AhnJ7zGHBylBTJL+puIRMwNBibqNTFFKAKU3MYUpbzsDq5QAqn2ZKVB56/m8wyxAZKLXmA0ITf4Ks1NSmVC/wIzFKq8NQ4FdD+bPxH8z+6tplWQmlJeeG8xUtBJU+xSzBuHJnmLG0pmgn2CKb0yA9Amm/Ma8bM5rzMYCJeweMlMbmOoRU/iceXXuvMIsrVBC8oCZ2MGE9i6ztUT5R68NusuUtjDT31B/MzNbmOouM7KFCfU9pjVKKO4wK3uY2R3mzh5mfIeZ28MM7zBje5jgCFPfZkYWMavbTLCT2V7EJmb5S3XJbGxi5jeZlWfK26PcZBY2MeNfrB9mJmk0jeOmPQAAAABJRU5ErkJggg==`; // assets/new/svgs/profile_img.svg\n    this.logoUrl = \"\";\n    this.permissions = [];\n    this.isAuthenticated$ = this.userService.isAuthenticated;\n    this.fetchCurrentUser();\n    this.isAuthenticated$.subscribe(isUpdated => {\n      if (isUpdated) {\n        this.buildNavList();\n      }\n      this.permissions = this.jwtTokenService.getPermissions();\n    });\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.isLoading = true;\n      } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {\n        this.isLoading = false;\n      }\n    });\n  }\n  fetchCurrentUser() {\n    this.userService.currentUser.subscribe(userData => {\n      this.currentUser = userData;\n      this.primaryRole = this.currentUser?.roles?.find(o => o?.isPrimaryDesignation) || this.currentUser?.roles?.[0];\n      this.loadProfileImage(userData?.profileImage);\n      this.getLogoImage(userData);\n    });\n  }\n  toggleSideNav() {\n    this.leftMenuMode = this.leftMenuMode === \"push\" ? \"over\" : \"push\";\n    this.isSideNavExpanded = this.leftMenuMode === \"push\";\n    localStorage.setItem(\"leftMenuMode\", `${this.leftMenuMode}`);\n  }\n  hoverSideNav(value) {\n    if (this.leftMenuMode === \"over\") {\n      this.isSideNavExpanded = value;\n    }\n  }\n  onSelectMenuItem(item, items) {\n    if (item?.subMenus?.length) {\n      items.forEach(o => {\n        if (item.name !== o.name) o[\"expanded\"] = false;\n      });\n      item[\"expanded\"] = !item?.expanded;\n    } else if (item?.path) {\n      this.router.navigate([item?.path]);\n    }\n  }\n  buildNavList() {\n    const configNav = navList => navList.reduce((list, item) => {\n      if (item?.subMenus?.length) {\n        const children = configNav(item?.subMenus);\n        item.subMenus = children;\n        item.hasAccess = children.some(item => item.hasAccess);\n        item.class = item?.name?.toLowerCase()?.replace(/ & /g, \"-\")?.replace(/ /g, \"-\");\n      } else if (item?.acm?.length) {\n        item.hasAccess = this.acmService.hasACMAccess(item?.acm);\n      } else if (item?.roles?.length) {\n        item.hasAccess = this.acmService.hasRoleAccess(item?.roles);\n      } else {\n        item.hasAccess = item?.hasAccess ?? false;\n      }\n      delete item?.acm;\n      list.push(item);\n      return list;\n    }, []);\n    const navList = configNav(MENU_LIST());\n    this.navList = navList;\n  }\n  ngOnDestroy() {}\n  loadProfileImage(profileImage) {\n    if (profileImage) {\n      this.userService.filePreview(profileImage).subscribe(res => {\n        const mediaType = \"image/jpeg\";\n        const arrayBufferView = new Uint8Array(res);\n        const file = new Blob([arrayBufferView], {\n          type: mediaType\n        });\n        const myReader = new FileReader();\n        myReader.onloadend = e => {\n          this.profileImage = myReader.result;\n        };\n        myReader.readAsDataURL(file);\n      });\n    }\n  }\n  getLogoImage(user) {\n    if (!user?.id) return;\n    this.userService.getLogo().subscribe(res => {\n      const mediaType = \"image/jpeg\";\n      const arrayBufferView = new Uint8Array(res);\n      const file = new Blob([arrayBufferView], {\n        type: mediaType\n      });\n      const myReader = new FileReader();\n      myReader.onloadend = e => {\n        this.logoUrl = myReader.result;\n      };\n      myReader.readAsDataURL(file);\n    }, err => {\n      this.toastr.error(\"Logo is not found\");\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: Router\n    }, {\n      type: UserService\n    }, {\n      type: AcmService\n    }, {\n      type: ToastrService\n    }];\n  }\n};\nMenuLayoutComponent = __decorate([Component({\n  selector: \"app-menu-layout\",\n  standalone: true,\n  imports: [AsyncPipe, DatePipe, RouterLink, RouterLinkActive, NgClass, NgTemplateOutlet, NgScrollbarModule, BsDropdownModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], MenuLayoutComponent);\nexport { MenuLayoutComponent };\nconst MENU_LIST = () => [{\n  name: \"Account Search\",\n  icon: \"nav_dash_icon\",\n  path: \"/encollect/v1/dashboard\",\n  // hasAccess: true,\n  acm: [ACMP.CanSearchAccounts]\n}, {\n  name: \"User Management\",\n  icon: \"nav_user_mgmt_icon\",\n  subMenus: [{\n    name: \"Agency Empanelment\",\n    subMenus: [{\n      name: \"Add Agency\",\n      path: \"/encollect/agency/v1/agencyEmpanelment-create\",\n      acm: [ACMP.CanCreateAgency]\n    }, {\n      name: \"Search Agency\",\n      path: \"/encollect/agency/v1/agencyEmpanelment-search\",\n      acm: [ACMP.CanSearchAgency]\n    }]\n  }, {\n    name: \"Agent Empanelment\",\n    subMenus: [{\n      name: \"Add Agent\",\n      path: \"/encollect/agent/v1/create\",\n      acm: [ACMP.CanCreateAgent]\n    }, {\n      name: \"Search Agent\",\n      path: \"/encollect/agent/v1/search\",\n      acm: [ACMP.CanSearchAgent]\n    }]\n  }, {\n    name: \"Staff Empanelment\",\n    subMenus: [{\n      name: \"Add Staff\",\n      path: \"/encollect/staff/v1/create-collection-staff\",\n      acm: [ACMP.CanCreateStaff]\n    }, {\n      name: \"Search Staff\",\n      path: \"/encollect/staff/v1/search-collection-staff\",\n      acm: [ACMP.CanSearchStaff]\n    }]\n  }, {\n    name: \"Locked Profiles\",\n    path: \"/encollect/users/v1/locked-profiles\",\n    hide: true\n    // acm: [ACM.UMLockedProfilesScreen], // TODO: ACM Configuration\n  }, {\n    name: \"Bulk Upload\",\n    subMenus: [{\n      name: \"User Creation Upload\",\n      path: \"/encollect/users/v1/bulk-user-upload\",\n      acm: [ACMP.CanUploadBulkUser]\n    }, {\n      name: \"User Creation Upload Status\",\n      path: \"/encollect/users/v1/bulk-user-upload-status\",\n      acm: [ACMP.CanSearchBulkUserUploadStatus]\n    }, {\n      name: \"Bulk Enable/Disable Users\",\n      path: \"/encollect/users/v1/bulk-upload-enable-disable\",\n      acm: [ACMP.CanUploadBulkEnableDisableUser]\n    }, {\n      name: \"Bulk Enable/Disable Users Status\",\n      path: \"/encollect/users/v1/bulk-upload-enable-disable-status\",\n      acm: [ACMP.CanSearchBulkEnableDisableUserStatus]\n    }]\n  }]\n}, {\n  name: \"Allocation\",\n  icon: \"nav_allocation_icon\",\n  subMenus: [{\n    name: \"Agency Bulk Upload\",\n    subMenus: [{\n      name: \"Agency Bulk Allocation Account Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-batch\",\n      acm: [ACMP.CanUploadPrimaryAllocationBatch]\n    }, {\n      name: \"Agency Bulk Allocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-batch-customerLevel\",\n      acm: [ACMP.CanUploadPrimaryAllocationBatch]\n    }, {\n      name: \"Agency Bulk Deallocation Account Level\",\n      path: \"encollect/allocation/v1/upload-agency-unallocation-batch\",\n      acm: [ACMP.CanUploadPrimaryDeAllocationBatch]\n    }, {\n      name: \"Agency Bulk Deallocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-agency-unallocation-batch-customerLevel\",\n      acm: [ACMP.CanUploadPrimaryDeAllocationBatch]\n    }, {\n      name: \"Agency Allocation Status\",\n      path: \"encollect/allocation/v1/primary-allocation-status\",\n      acm: [ACMP.CanSearchPrimaryAllocationBatchStatus]\n    }, {\n      name: \"Agency Deallocation Status\",\n      path: \"encollect/allocation/v1/primary-unallocation-status\",\n      acm: [ACMP.CanSearchPrimaryDeAllocationBatchStatus]\n    }]\n  }, {\n    name: \"Agent Bulk Upload\",\n    subMenus: [{\n      name: \"Agent Bulk Allocation Account Level\",\n      path: \"encollect/allocation/v1/upload-collector-allocation-batch\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryAllocationBatch]\n    }, {\n      name: \"Agent Bulk Allocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-collector-allocation-batch-customerLevel\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryAllocationBatch]\n    }, {\n      name: \"Agent Bulk Deallocation Account Level\",\n      path: \"encollect/allocation/v1/upload-collector-unallocation-batch\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryDeAllocationBatch]\n    }, {\n      name: \"Agent Bulk Deallocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-collector-unallocation-batch-customerLevel\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryDeAllocationBatch]\n    }, {\n      name: \"Agent Allocation Status\",\n      path: \"encollect/allocation/v1/secondary-allocation-status\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanSearchSecondaryAllocationBatchStatus]\n    }, {\n      name: \"Agent Deallocation Status\",\n      path: \"encollect/allocation/v1/secondary-unallocation-status\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanSearchSecondaryDeAllocationBatchStatus]\n    }]\n  }, {\n    name: \"Allocation Owner Bulk Upload\",\n    subMenus: [{\n      name: \"Allocation Owner Bulk Upload Account Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-owner\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadAllocationOwnerBatch]\n    }, {\n      name: \"Allocation Owner Bulk Upload Customer Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-owner-customerLevel\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadAllocationOwnerBatch]\n    }, {\n      name: \"Allocation Owner Upload Status\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-owner-status\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanSearchAllocationOwnerBatchStatus]\n    }]\n  }, {\n    name: \"Allocation Filters\",\n    subMenus: [{\n      name: \"Agency Allocation by Filters\",\n      path: \"encollect/allocation/v1/primary-allocation-filters\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUpdatePrimaryAllocationByFilter]\n    }, {\n      name: \"Agent Allocation by Filters\",\n      path: \"encollect/allocation/v1/secondary-allocation-filters\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUpdateSecondaryAllocationByFilter]\n    }]\n  }]\n}, {\n  name: \"Trails\",\n  icon: \"nav_trails_icon\",\n  subMenus: [{\n    name: \"Bulk Trail Upload\",\n    path: \"encollect/allocation/v1/bulk-trail\",\n    icon: \"fa fa-upload\",\n    acm: [ACMP.CanUploadBulkTrail]\n  }, {\n    name: \"Trail Upload Status\",\n    path: \"encollect/allocation/v1/bulk-trail-status\",\n    icon: \"fa fa-upload\",\n    acm: [ACMP.CanSearchBulkTrailStatus]\n  }]\n}, {\n  name: \"Payments\",\n  icon: \"nav_payments_icon\",\n  subMenus: [{\n    name: \"Receive Money from Collector\",\n    path: \"encollect/payments/v1/receive-money-from-collector\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanAcknowledgeReceipt]\n  }, {\n    name: \"Batch of Payments\",\n    subMenus: [{\n      name: \"Create Batch of Payments\",\n      path: \"encollect/payments/v1/create-batch-of-payments\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreateBatch]\n    }, {\n      name: \"Search and Print Batch\",\n      path: \"encollect/payments/v1/print-batch-list\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSearchBatch, ACMP.CanPrintBatch]\n    }, {\n      name: \"Search and Edit Batch\",\n      path: \"encollect/payments/v1/search-and-edit-batch\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSearchBatch, ACMP.CanUpdateBatch]\n    }, {\n      name: \"Receive Batch of Payments at Branch\",\n      path: \"encollect/payments/v1/receive-batch-of-payments\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanAcknowledgeBatch]\n    }]\n  }, {\n    name: \"Deposit Slip\",\n    subMenus: [{\n      name: \"Create Deposit Slip\",\n      path: \"encollect/payments/v1/create-pay-slip\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreatePIS]\n    }, {\n      name: \"Search and View Deposit Slip\",\n      path: \"encollect/payments/v1/search-and-view-pay-in-slip\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSearchPIS]\n    }, {\n      name: \"Acknowledge Deposit Slip\",\n      path: \"encollect/payments/v1/central_ops_acknowledging\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanAcknowledgePIS]\n    }]\n  }, {\n    name: \"Receipts\",\n    subMenus: [{\n      name: \"Issue Receipt to Walk-in Customer\",\n      path: \"encollect/payments/v1/Walkin-customer-receipt\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreateWalkinReceipt]\n    }, {\n      name: \"Send Duplicate Receipt\",\n      path: \"encollect/payments/v1/search-and-send-duplicate-email-e-receipt-and-SMS\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSendDuplicateReceipt]\n    }, {\n      name: \"Raise Receipt Cancellation Request\",\n      path: \"encollect/payments/v1/reciept-cancellation-request\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreateReceiptCancellationRequest]\n    }, {\n      name: \"Action Receipt Cancellation Request\",\n      path: \"encollect/payments/v1/reciept-cancellation-request-approval-reject\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanApproveReceiptCancellationRequest, ACMP.CanRejectReceiptCancellationRequest]\n    }]\n  }, {\n    name: \"Bulk Payments Upload\",\n    subMenus: [{\n      name: \"Bulk Payments Upload\",\n      path: \"encollect/payments/v1/bulk-payments\",\n      icon: \"fa fa-upload\",\n      acm: [ACMP.CanCreatePIS]\n    }, {\n      name: \"Bulk Payments Upload Status\",\n      path: \"encollect/payments/v1/bulk-payments-upload-status\",\n      icon: \"fa fa-upload\",\n      acm: [ACMP.CanCreatePIS]\n    }]\n  }, {\n    name: \"Download Payment Report\",\n    path: \"encollect/payments/v1/download-payment-report\",\n    iconFa: \"fa fa-credit-card\",\n    acm: [ACMP.CanDownloadPaymentReport]\n  }]\n}, {\n  name: \"Geo Report\",\n  icon: \"nav_geo_report_icon\",\n  subMenus: [{\n    name: \"User Travel Report\",\n    path: \"travel-report/travel-report\",\n    icon: \"fa fa-users\",\n    acm: [ACMP.CanSearchTravelReport]\n  }]\n}, {\n  name: \"Digital ID\",\n  icon: \"nav_digital_id_icon\",\n  subMenus: [{\n    name: \"Digital ID Card\",\n    path: \"digital/card\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanViewDigitalIDCard]\n  }]\n}, {\n  name: \"Settlement\",\n  icon: \"nav_settlement_icon\",\n  subMenus: [{\n    name: \"Find Eligible Cases\",\n    path: \"settlement/eligible-cases\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanFlagSettlementAsEligible] //CanViewDigitalIDCard\n  }, {\n    name: \"Request Settlement\",\n    path: \"settlement/request-settlement\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanRequestSettlement]\n  }, {\n    name: \"My Requests\",\n    path: \"settlement/my-settlement-summary\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanViewMySettlement]\n  }, {\n    name: \"My Action Queue\",\n    path: \"settlement/my-settlement-queue-summary\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanViewMyQueueSettlement]\n  }, {\n    name: \"Search Settlements\",\n    path: \"settlement/settlement-report\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanViewMySettlement] //CanViewSettlementReport\n  }]\n}, {\n  name: \"Target Setting\",\n  // TODO: Needs to remove if its not in use\n  icon: \"nav_target_setting_icon\",\n  hide: true,\n  subMenus: [{\n    name: \"Upload Targets\",\n    path: \"target/upload-budgeted-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"Upload Budgeted Target Status\",\n    path: \"target/budgeted-target-file-upload-status\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"View Budgeted Targets\",\n    path: \"target/view-budgeted-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"Create Target\",\n    path: \"target/create-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"Target Listing\",\n    path: \"target/search-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }]\n}, {\n  name: \"Curing Tools\",\n  icon: \"nav_curing_tools_icon\",\n  hide: true,\n  subMenus: [{\n    name: \"Request Settlement\",\n    path: \"settlement/acs-request-settlement\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Requests\",\n    path: \"settlement/acs-mysettlement\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Settlement Queue\",\n    path: \"settlement/acs-settlement-queue\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Request Cure\",\n    path: \"encollect/cure/request-cure\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Cure\",\n    path: \"encollect/cure/my-cure\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Cure Queue\",\n    path: \"encollect/cure/my-cure-queue\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Add/Initiate Legal Case\",\n    path: \"encollect/legal-custom/create-legal\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Legal Case\",\n    path: \"encollect/legal-custom/my-legal\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Legal Queue\",\n    path: \"encollect/legal-custom/myqueue-legal\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Bulk Upload of Hearing Date\",\n    path: \"encollect/legal-custom/bulkupload-hearing\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Status of Bulk Upload of Hearing Date\",\n    path: \"encollect/legal-custom/bulkupload-hearing-status\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Bulk Upload to Initiate Case\",\n    path: \"encollect/legal-custom/bulkupload-initiate\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Status of Bulk Upload Initiate Case\",\n    path: \"encollect/legal-custom/bulkupload-initiate-status\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Initiate Legal Request\",\n    path: \"encollect/legal/initiate-legal-request\",\n    icon: \"fa fa-users\",\n    hide: true\n  }, {\n    name: \"My Legal Queue\",\n    path: \"encollect/legal/my-legal-queue\",\n    icon: \"fa fa-users\",\n    hide: true\n  }, {\n    name: \"Create Repossession\",\n    path: \"encollect/repossession/create-repossession\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Repossession\",\n    path: \"encollect/repossession/my-repossession\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Queue Repossession\",\n    path: \"encollect/repossession/myqueue-repossession\",\n    icon: \"fa fa-users\",\n    acm: []\n  }]\n}, {\n  name: \"Segmentation & Treatment\",\n  icon: \"nav_segmentation_icon\",\n  subMenus: [{\n    name: \"Segmentation\",\n    subMenus: [{\n      name: \"Create Segment\",\n      path: \"segmentation/create-segmentation\",\n      acm: [ACMP.CanCreateSegment]\n    }, {\n      name: \"Search Segments\",\n      path: \"segmentation/search-segmentation\",\n      acm: [ACMP.CanSearchSegment]\n    }, {\n      name: \"Sequence Segments\",\n      path: \"segmentation/segmentation-sequence\",\n      acm: [ACMP.CanSequenceSegment]\n    }, {\n      name: \"Compare Segments\",\n      path: \"segmentation/compare-segmentation\",\n      acm: [ACMP.CanCompareSegment]\n    }]\n  }, {\n    name: \"Treatment\",\n    subMenus: [{\n      name: \"Create Treatment\",\n      path: \"treatment/create-treatment-step1\",\n      acm: [ACMP.CanCreateTreatment]\n    }, {\n      name: \"Search Treatments\",\n      path: \"treatment/search-treatment\",\n      acm: [ACMP.CanSearchTreatment]\n    }, {\n      name: \"Sequence Treatments\",\n      path: \"treatment/treatment-sequence\",\n      acm: [ACMP.CanSequenceTreatement]\n    }]\n  }, {\n    name: \"Clear Segment / Treatment\",\n    path: \"segmentation/clear\",\n    acm: [ACMP.CanClearSegmentAndTreatementStamping]\n  }, {\n    name: \"Customer Search\",\n    path: \"coming-soon\",\n    hide: true\n  }, {\n    name: \"Account Search\",\n    path: \"coming-soon\",\n    hide: true\n  }]\n}, {\n  name: \"System Settings\",\n  icon: \"nav_system_settings_icon\",\n  subMenus: [{\n    name: \"Account Upload\",\n    subMenus: [{\n      name: \"Bulk Account Upload\",\n      path: \"settings/upload-account-import-master\",\n      acm: [ACMP.CanUploadBulkAccounts]\n    }, {\n      name: \"Account Upload Status\",\n      path: \"settings/upload-account-import-master-status\",\n      acm: [ACMP.CanSearchBulkAccountsUploadStatus]\n    }]\n  }, {\n    name: \"Masters Upload\",\n    subMenus: [{\n      name: \"Bulk Upload Masters\",\n      path: \"settings/bulk-upload-master\",\n      acm: [ACMP.CanUploadMasters]\n    }, {\n      name: \"Masters Upload Status\",\n      path: \"settings/bulk-upload-master-status\",\n      acm: [ACMP.CanSearchUploadMastersStatus]\n    }, {\n      name: \"View and Disable Masters\",\n      path: \"settings/view-masters\",\n      acm: [ACMP.CanSearchMasters] //SSViewDisableMastersScreen\n    }]\n  }, {\n    name: \"Permissions\",\n    subMenus: [{\n      name: \"Define Permission Schemes\",\n      path: \"settings/permissions/define-permission-group\",\n      acm: [ACMP.CanCreatePermissionScheme]\n    }, {\n      name: \"Search Permission Schemes\",\n      path: \"settings/permissions/search-permission-groups\",\n      acm: [ACMP.CanViewPermissionSchemes]\n    }, {\n      name: \"Assign Permission Scheme to Designations\",\n      path: \"settings/permissions/assign-designations-to-permission-groups\",\n      acm: [ACMP.CanViewDesignationSchemeDetails]\n    }, {\n      name: \"Search Permissions\",\n      path: \"settings/permissions/search-permissions\",\n      acm: [ACMP.CanSearchPermissions]\n    }]\n  }, {\n    name: \"Define ACM\",\n    hide: true,\n    subMenus: [{\n      name: \"Web\",\n      path: \"settings/define-web-acm\",\n      acm: [ACMP.CanDefineACM]\n    }, {\n      name: \"Mobile\",\n      path: \"settings/define-mobile-acm\",\n      acm: [ACMP.CanDefineACM]\n    }]\n  }, {\n    name: \"Basic UI Settings\",\n    path: \"settings/main-settings\",\n    hide: true\n  }, {\n    name: \"Disposition Code Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Create Disposition Code Group\",\n      path: \"settings/disposition-group-config\"\n    }, {\n      name: \"Create Disposition Code\",\n      path: \"settings/disposition-code-config\"\n    }]\n  }, {\n    name: \"Deposit Bank Account Number Config\",\n    path: \"settings/disposition-account-number\",\n    hide: true\n  }, {\n    name: \"Allocations\",\n    hide: true,\n    subMenus: [{\n      name: \"Allocation Configuration\",\n      path: \"settings/allocation-config\"\n    }, {\n      name: \"Bucket Configuration\",\n      path: \"settings/allocation-bucket-config\"\n    }]\n  }, {\n    name: \"Payments\",\n    hide: true,\n    subMenus: [{\n      name: \"Offline Receipts Configuration\",\n      path: \"settings/payments/offline-receipt\"\n    }, {\n      name: \"Transaction Series Configuration\",\n      path: \"settings/payments/transaction-series\"\n    }, {\n      name: \"Mode of Payments Configuration\",\n      path: \"settings/payments/mode-of-payments\"\n    }, {\n      name: \"Issue Receipt Masters Configuration\",\n      path: \"settings/payments/denominations\"\n    }]\n  }, {\n    name: \"Geography Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Geography Master\",\n      path: \"settings/upload-geography-master\"\n    }, {\n      name: \"Upload Geography Master Status\",\n      path: \"settings/upload-geography-master-status\"\n    }, {\n      name: \"Search Geography Master\",\n      path: \"settings/geography-master-search\"\n    }]\n  }, {\n    name: \"Area Code Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Area Code Master\",\n      path: \"settings/upload-area-code-master\"\n    }, {\n      name: \"Upload Area Code Master Status\",\n      path: \"settings/upload-area-code-master-status\"\n    }, {\n      name: \"Search Area Code Master\",\n      path: \"settings/area-code-master-search\"\n    }]\n  }, {\n    name: \"Bank Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Bank Master\",\n      path: \"settings/upload-bank-master\"\n    }, {\n      name: \"Upload Bank Master Status\",\n      path: \"settings/upload-bank-master-status\"\n    }, {\n      name: \"Search Bank Master\",\n      path: \"settings/bank-master-search\"\n    }]\n  }, {\n    name: \"Account Detail Label Customization\",\n    path: \"settings/account-detail-label-customization\",\n    roles: [],\n    hide: true\n  }, {\n    name: \"Base Branch Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Base Branch Master\",\n      path: \"settings/upload-branch-master\"\n    }, {\n      name: \"Upload Base Branch Master Status\",\n      path: \"settings/upload-branch-master-status\"\n    }, {\n      name: \"Search Base Branch Master\",\n      path: \"settings/branch-master-search\"\n    }]\n  }, {\n    name: \"Workflow\",\n    hide: true,\n    subMenus: [{\n      name: \"Create Workflow\",\n      path: \"/workflows\"\n    }, {\n      name: \"Edit Workflow\",\n      path: \"/workflows/edit-workflow\"\n    }, {\n      name: \"View Workflow\",\n      path: \"/workflows/view-workflow\"\n    }, {\n      name: \"Search Workflow\",\n      path: \"/workflows/search-workflow\"\n    }]\n  }, {\n    name: \"Product Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Configure Product Group\",\n      path: \"settings/product-group-config\"\n    }, {\n      name: \"Configure Product\",\n      path: \"settings/product-config\"\n    }, {\n      name: \"Configure Sub Product\",\n      path: \"settings/sub-product-config\"\n    }]\n  }, {\n    name: \"Account Search Scope\",\n    path: \"settings/account-search-scope\",\n    roles: [\"SYSTEMADMIN\", \"BankToBackEndInternalBIHP\"],\n    // acm: [ACM.SSAccountSearchScopeScreen],\n    hide: true\n  }]\n}, {\n  name: \"Communication\",\n  icon: \"nav_communication_icon\",\n  subMenus: [{\n    name: \"Search Communication Templates\",\n    path: \"communication/search-communication-templates\",\n    acm: [ACMP.CanSearchCommunicationTemplate]\n  }, {\n    name: \"Create Communication Template\",\n    path: \"communication/create-communication-template\",\n    acm: [ACMP.CanCreateCommunicationTemplate]\n  }, {\n    name: \"Search Communication Triggers\",\n    path: \"communication/search-communication-triggers\",\n    acm: [ACMP.CanSearchCommunicationTrigger]\n  }, {\n    name: \"Create Communication Trigger\",\n    path: \"communication/create-communication-trigger\",\n    acm: [ACMP.CanCreateCommunicationTrigger]\n  }]\n}, {\n  name: \"Reports\",\n  icon: \"nav_reports_icon\",\n  subMenus: [{\n    name: \"Allocation Reports\",\n    subMenus: [{\n      name: \"Agency Allocation Gap Report\",\n      path: \"reports/agency-allocation-gap\",\n      acm: [ACMP.CanViewAgencyAllocationGapReport]\n    }, {\n      name: \"Agent Allocation Gap Report\",\n      path: \"reports/agent-allocation-gap\",\n      acm: [ACMP.CanViewAgentAllocationGapReport]\n    }, {\n      name: \"Allocated vs Achieved Report\",\n      path: \"reports/allocated-vs-achieved\",\n      acm: [ACMP.CanViewAllocatedvsArchievedReport]\n    }, {\n      name: \"Agency Allocation Gap Report (Old)\",\n      path: \"reports/agency-gap-mis-report\",\n      acm: [ACMP.CanViewAgencyAllocationGapReport]\n    }, {\n      name: \"Agent Allocation Gap Report (Old)\",\n      path: \"reports/agent-gap-mis-report\",\n      acm: [ACMP.CanViewAgentAllocationGapReport]\n    }, {\n      name: \"Allocated vs Achieved Report (Old)\",\n      path: \"reports/allocated-vs-achieved-report\",\n      acm: [ACMP.CanViewAllocatedvsArchievedReport]\n    }]\n  }, {\n    name: \"Trail Reports\",\n    subMenus: [{\n      name: \"Trail Gap Report\",\n      path: \"reports/trail-gap-report\",\n      acm: [ACMP.CanViewTrailGapReport]\n    }, {\n      name: \"Trail Gap Report\",\n      path: \"reports/trail-gap-report\",\n      acm: [ACMP.CanViewTrailGapReport]\n    }, {\n      name: \"Trail History Report\",\n      path: \"reports/trail-history-report\",\n      acm: [ACMP.CanViewTrailHistoryReport]\n    }, {\n      name: \"Trail Intensity Report\",\n      path: \"reports/trail-intensity-report\",\n      acm: [ACMP.CanViewTrailIntensityReport]\n    }]\n  }, {\n    name: \"Payment Report\",\n    path: \"reports/payment-report\",\n    acm: [ACMP.CanViewPaymentReport]\n  }, {\n    name: \"Money Movement Report\",\n    path: \"reports/money-movement-report\",\n    acm: [ACMP.CanViewMoneyMovementReport]\n  }, {\n    name: \"Attendance Report\",\n    path: \"attendance/attendance-report\",\n    acm: [ACMP.CanViewAttendanceReport]\n  }, {\n    name: \"Communication History Report\",\n    path: \"reports/communication-history-report\",\n    acm: [ACMP.CanViewCommunicationHistoryReport]\n  }, {\n    name: \"Account Dashboard Report\",\n    path: \"reports/account-dashboard-report\",\n    acm: [ACMP.CanViewAccountDashboardReport]\n  }, {\n    name: \"Performance Report\",\n    path: \"reports/performance-report\",\n    acm: [ACMP.CanViewPerformanceReport]\n  }, {\n    name: \"Supervisory Report\",\n    path: \"reports/supervisory-report\",\n    acm: [ACMP.CanViewSupervisoryReport]\n  }, {\n    name: \"Collection Intensity Report\",\n    path: \"reports/collection-intensity-report\",\n    acm: [ACMP.CanViewCollectionIntensityReport]\n  }, {\n    name: \"Collection Trend Report\",\n    path: \"reports/collection-trend-report\",\n    acm: [ACMP.CanViewCollectionTrendReport]\n  }, {\n    name: \"Visit Intensity Report\",\n    path: \"reports/visit-intensity-report\",\n    acm: [ACMP.CanViewVisitIntensityReport]\n  }, {\n    name: \"Collection Dashboard\",\n    path: \"reports/collection-dashboard\",\n    hide: false\n  }, {\n    name: \"Allocation vs Collections vs Trails Analysis Report\",\n    path: \"reports/allocation-collection-trails-report\",\n    hide: true\n  }, {\n    name: \"Target vs Actual Report\",\n    path: \"reports/target-actual-analysis-report\",\n    hide: true\n  }, {\n    name: \"Daily Legal Report\",\n    path: \"reports/daily-legal-report\",\n    hide: true\n  }, {\n    name: \"Daily Repossession Report\",\n    path: \"reports/daily-repo-report\",\n    hide: true\n  }, {\n    name: \"Customer Contact Report\",\n    path: \"reports/ccd-report\",\n    acm: [ACMP.CanViewCustomerContactReport]\n  }, {\n    name: \"Cash Wallet Limit Report\",\n    path: \"reports/cash-wallet-limit-report\",\n    acm: [ACMP.CanViewCashWalletLimitReport] //CanViewCashWalletLimitReport\n  }]\n}, {\n  name: \"Insights\",\n  icon: \"nav_insights_icon\",\n  subMenus: [{\n    name: \"Primary Allocation Insights\",\n    path: \"insights/primary-allocation-insights\",\n    acm: [ACMP.CanViewAgencyAllocationGapReport]\n  }, {\n    name: \"Secondary Allocation Insights\",\n    path: \"insights/secondary-allocation-insights\",\n    acm: [ACMP.CanViewAgentAllocationGapReport]\n  }, {\n    name: \"Trail Gap Insights\",\n    path: \"insights/trail-gap-insights\",\n    acm: [ACMP.CanViewTrailGapReport]\n  }, {\n    name: \"Allocated vs Achieved Insights\",\n    path: \"insights/allocated-vs-achieved-insights\",\n    acm: [ACMP.CanViewAllocatedvsArchievedReport]\n  }, {\n    name: \"Money Movement Insights (Agency Staff)\",\n    path: \"insights/money-movement-insights-agency-staff\",\n    acm: [ACMP.CanViewMoneyMovementReport]\n  }, {\n    name: \"Money Movement Insights (Bank Staff)\",\n    path: \"insights/money-movement-insights-bank-staff\",\n    acm: [ACMP.CanViewMoneyMovementReport]\n  }]\n}];", "map": {"version": 3, "names": ["Component", "inject", "NavigationCancel", "NavigationEnd", "NavigationError", "NavigationStart", "Router", "RouterLink", "RouterLinkActive", "interval", "map", "UserService", "ACMP", "AcmService", "ToastrService", "JWTTokenService", "AsyncPipe", "DatePipe", "Ng<PERSON><PERSON>", "NgTemplateOutlet", "NgScrollbarModule", "BsDropdownModule", "leftMenuMode", "localStorage", "getItem", "MenuLayoutComponent", "constructor", "router", "userService", "acmService", "toastr", "jwtTokenService", "currentTime$", "pipe", "Date", "isLoading", "isSideNavExpanded", "profileImage", "logoUrl", "permissions", "isAuthenticated$", "isAuthenticated", "fetchCurrentUser", "subscribe", "isUpdated", "buildNavList", "getPermissions", "events", "event", "currentUser", "userData", "primaryRole", "roles", "find", "o", "isPrimaryDesignation", "loadProfileImage", "getLogoImage", "toggleSideNav", "setItem", "hoverSideNav", "value", "onSelectMenuItem", "item", "items", "subMenus", "length", "for<PERSON>ach", "name", "expanded", "path", "navigate", "config<PERSON>av", "navList", "reduce", "list", "children", "hasAccess", "some", "class", "toLowerCase", "replace", "acm", "hasACMAccess", "hasRoleAccess", "push", "MENU_LIST", "ngOnDestroy", "filePreview", "res", "mediaType", "arrayBufferView", "Uint8Array", "file", "Blob", "type", "my<PERSON><PERSON><PERSON>", "FileReader", "onloadend", "e", "result", "readAsDataURL", "user", "id", "get<PERSON>ogo", "err", "error", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0", "icon", "CanSearchAccounts", "CanCreateAgency", "CanSearchAgency", "CanCreateAgent", "CanSearchAgent", "CanCreateStaff", "CanSearchStaff", "hide", "CanUploadBulkUser", "CanSearchBulkUserUploadStatus", "CanUploadBulkEnableDisableUser", "CanSearchBulkEnableDisableUserStatus", "CanUploadPrimaryAllocationBatch", "CanUploadPrimaryDeAllocationBatch", "CanSearchPrimaryAllocationBatchStatus", "CanSearchPrimaryDeAllocationBatchStatus", "CanUploadSecondaryAllocationBatch", "CanUploadSecondaryDeAllocationBatch", "CanSearchSecondaryAllocationBatchStatus", "CanSearchSecondaryDeAllocationBatchStatus", "CanUploadAllocationOwnerBatch", "CanSearchAllocationOwnerBatchStatus", "CanUpdatePrimaryAllocationByFilter", "CanUpdateSecondaryAllocationByFilter", "CanUploadBulkTrail", "CanSearchBulkTrailStatus", "CanAcknowledgeReceipt", "CanCreateBatch", "CanSearchBatch", "CanPrintBatch", "CanUpdateBatch", "CanAcknowledgeBatch", "CanCreatePIS", "CanSearchPIS", "CanAcknowledgePIS", "CanCreateWalkinReceipt", "CanSendDuplicateReceipt", "CanCreateReceiptCancellationRequest", "CanApproveReceiptCancellationRequest", "CanRejectReceiptCancellationRequest", "iconFa", "CanDownloadPaymentReport", "CanSearchTravelReport", "CanViewDigitalIDCard", "CanFlagSettlementAsEligible", "CanRequestSettlement", "CanViewMySettlement", "CanViewMyQueueSettlement", "CanCreateSegment", "CanSearchSegment", "CanSequenceSegment", "CanCompareSegment", "CanCreateTreatment", "CanSearchTreatment", "CanSequenceTreatement", "CanClearSegmentAndTreatementStamping", "CanUploadBulkAccounts", "CanSearchBulkAccountsUploadStatus", "CanUploadMasters", "CanSearchUploadMastersStatus", "CanSearchMasters", "CanCreatePermissionScheme", "CanViewPermissionSchemes", "CanViewDesignationSchemeDetails", "CanSearchPermissions", "CanDefineACM", "CanSearchCommunicationTemplate", "CanCreateCommunicationTemplate", "CanSearchCommunicationTrigger", "CanCreateCommunicationTrigger", "CanViewAgencyAllocationGapReport", "CanViewAgentAllocationGapReport", "CanViewAllocatedvsArchievedReport", "CanViewTrailGapReport", "CanViewTrailHistoryReport", "CanViewTrailIntensityReport", "CanViewPaymentReport", "CanViewMoneyMovementReport", "CanViewAttendanceReport", "CanViewCommunicationHistoryReport", "CanViewAccountDashboardReport", "CanViewPerformanceReport", "CanViewSupervisoryReport", "CanViewCollectionIntensityReport", "CanViewCollectionTrendReport", "CanViewVisitIntensityReport", "CanViewCustomerContactReport", "CanViewCashWalletLimitReport"], "sources": ["D:\\github\\sowreports\\SCB-ENCollect.FE.Sleek\\src\\app\\shared\\components\\menu-layout\\menu-layout.component.ts"], "sourcesContent": ["import { Component, inject, OnD<PERSON>roy } from \"@angular/core\";\r\nimport {\r\n  NavigationCancel,\r\n  NavigationEnd,\r\n  NavigationError,\r\n  NavigationStart,\r\n  Router,\r\n  RouterLink,\r\n  RouterLinkActive,\r\n} from \"@angular/router\";\r\nimport { interval, map, Observable } from \"rxjs\";\r\nimport { UserService } from \"src/app/authentication/user.service\";\r\nimport { ACMP, AcmService } from \"../../services\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { JWTTokenService } from \"../../services/jwt-token.service\";\r\nimport {\r\n  AsyncPipe,\r\n  DatePipe,\r\n  NgClass,\r\n  NgTemplateOutlet,\r\n} from \"@angular/common\";\r\nimport { NgScrollbarModule } from \"ngx-scrollbar\";\r\nimport { BsDropdownModule } from \"ngx-bootstrap/dropdown\";\r\n\r\ntype LeftMenuMode = \"push\" | \"over\";\r\nconst leftMenuMode: LeftMenuMode =\r\n  (localStorage.getItem(\"leftMenuMode\") as LeftMenuMode) || \"push\";\r\n\r\n@Component({\r\n  selector: \"app-menu-layout\",\r\n  standalone: true,\r\n  imports: [\r\n    AsyncPipe,\r\n    DatePipe,\r\n    RouterLink,\r\n    RouterLinkActive,\r\n    NgClass,\r\n    NgTemplateOutlet,\r\n    NgScrollbarModule,\r\n    BsDropdownModule,\r\n  ],\r\n  templateUrl: \"./menu-layout.component.html\",\r\n  styleUrls: [\"./menu-layout.component.scss\"],\r\n})\r\nexport class MenuLayoutComponent implements OnDestroy {\r\n  private jwtTokenService = inject(JWTTokenService);\r\n\r\n  public currentTime$ = interval(1000).pipe(map(() => new Date()));\r\n  public isAuthenticated$: Observable<any>;\r\n  public currentUser: any;\r\n  public primaryRole: any;\r\n  public isLoading: boolean = false;\r\n\r\n  public navList: NavItem[];\r\n\r\n  public isSideNavExpanded: boolean = leftMenuMode === \"push\";\r\n  public leftMenuMode: LeftMenuMode = leftMenuMode;\r\n  public profileImage:\r\n    | string\r\n    | ArrayBuffer = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOYAAADmCAAAAADkAaZnAAAFCElEQVR42u3cV7LbPAwFYO1/i6C61ahiq1j2zV+Sh8xkEsdVBGyA5FkBviHFIlIKvjuRwDM90zM984PM/52IZ3qmZ3qmZ3qmZ3qmZ9rM/M+JeKZneqZneqZneqZnOsr814l4pmd6pmd6pmd6pmd6ps3Mf5yIZ3qmZ3qmU8zZAeaxjeFn8mp/tJH5NbdFCH8kLPqTVcxTk8L1xNX4ZQnzmMPd5OSNGnwjz1rB48RNm/YrWQ30TK3g6WSTUOaUwEvJzhKZGl6NWsQxlwSAh5OSOSjYEnUWxaxhY3aCmOcSNmcSwzwn25WQrUKYRkqA6CyCaagEAKUFMI2VACUu84sgJSAkmREromBqwEnPmjkCVmrGzJNCY0LLl5kBYvZcmT2mEtSJJxOzywIA7HgyS0DOyJF5xFZCxpGJ3pgAMz8mfmMiTZ4B98YEhcI8I2YBikwIlaEyGxJmw40ZkTAzZsyJRAmKGbOgYcLKixkRMQ+smER9lhuzpWI2rJiZG0xwgjmxZp6w0tExzYvDYzZuMMlGIBjcYB7cYI5uME9OMCM3mBkrZsF4PpEwbw6smJqKecRgrljZEylzjOLwmAsRs+fFXGnekaiVGTMnYZbcmDRj0IEbk2RfnazcmGtCwNT8mBSv9hZ+zIXppInMXJlOmrjMpWA6aaIyDxHbcXZdgyNWUpKVHlJxaMyRZHEQcWMORLswJ5iKG3MmYabcmEeSfVjDjrkjeRPNjklxIhYf2TEpem3DkElwYW9gyKzxma0bzBqNuaCFYKitsGpDZKaeaRGToNM2DJkHgu0mQyZBcy4cmRPyF0WQsWQuFTJT82QiN6daeDIXzXM6wWYumLuUGLGuYEZNH2MZs2pCrAuZOc89yv2gDLkqdOY8Y3Tcij8T4yil5s/EmD17N5iDG8zZCWbsBjN1g1kJYCL8x0KjMyf0IBzoDtg1ETAn8+XeJIFpfE86FsE8mO6ucxHMyfTKaSODaTrW9jKYk9nmWk1CmGYrhEQK0+wrhlIKcwpNmJ0YZsLr0aRimjycGQVzJInJ6VhDUA8RczS4VjIIYu43L/iSURBz3HqcojpRzHHjYFuNspg7Pl2WkLnx3/WVLOZ+6+q9FMXcvuXMBTFNNta5GKbZ64NaBnNv+oWjxmce0NOFhkpQHXZN+EyMU1x0JzZT49wlUZozc8D7/8pu4MocCsxbXmHNkomLRIYGbJEAAGExcGL2ZP9EUkXPhalToAzGYGTOrCMgjjLvuoHpIxnCG6IKU+beIP1OwZsS1iaF7g2YXQbvTFi0H2DqBN6fKCkqrfU7mF1dJCF8OCrJypaOWecfF/5mTauegNnlCrglrZGZH3kYnxmaKkQmV+ST0OeYXQqsE2oMZqmAe9LelNklICBKmzG1AhnJ7zGHBylBTJL+puIRMwNBibqNTFFKAKU3MYUpbzsDq5QAqn2ZKVB56/m8wyxAZKLXmA0ITf4Ks1NSmVC/wIzFKq8NQ4FdD+bPxH8z+6tplWQmlJeeG8xUtBJU+xSzBuHJnmLG0pmgn2CKb0yA9Amm/Ma8bM5rzMYCJeweMlMbmOoRU/iceXXuvMIsrVBC8oCZ2MGE9i6ztUT5R68NusuUtjDT31B/MzNbmOouM7KFCfU9pjVKKO4wK3uY2R3mzh5mfIeZ28MM7zBje5jgCFPfZkYWMavbTLCT2V7EJmb5S3XJbGxi5jeZlWfK26PcZBY2MeNfrB9mJmk0jeOmPQAAAABJRU5ErkJggg==`; // assets/new/svgs/profile_img.svg\r\n  public logoUrl: string | ArrayBuffer = \"\";\r\n\r\n  permissions = [];\r\n  constructor(\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private acmService: AcmService,\r\n    private toastr: ToastrService\r\n  ) {\r\n    this.isAuthenticated$ = this.userService.isAuthenticated;\r\n    this.fetchCurrentUser();\r\n    this.isAuthenticated$.subscribe((isUpdated: boolean) => {\r\n      if (isUpdated) {\r\n        this.buildNavList();\r\n      }\r\n      this.permissions = this.jwtTokenService.getPermissions();\r\n    });\r\n    this.router.events.subscribe((event) => {\r\n      if (event instanceof NavigationStart) {\r\n        this.isLoading = true;\r\n      } else if (\r\n        event instanceof NavigationEnd ||\r\n        event instanceof NavigationCancel ||\r\n        event instanceof NavigationError\r\n      ) {\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  private fetchCurrentUser() {\r\n    this.userService.currentUser.subscribe((userData): any => {\r\n      this.currentUser = userData;\r\n      this.primaryRole =\r\n        this.currentUser?.roles?.find((o) => o?.isPrimaryDesignation) ||\r\n        this.currentUser?.roles?.[0];\r\n      this.loadProfileImage(userData?.profileImage);\r\n      this.getLogoImage(userData);\r\n    });\r\n  }\r\n\r\n  toggleSideNav() {\r\n    this.leftMenuMode = this.leftMenuMode === \"push\" ? \"over\" : \"push\";\r\n    this.isSideNavExpanded = this.leftMenuMode === \"push\";\r\n    localStorage.setItem(\"leftMenuMode\", `${this.leftMenuMode}`);\r\n  }\r\n\r\n  hoverSideNav(value: boolean) {\r\n    if (this.leftMenuMode === \"over\") {\r\n      this.isSideNavExpanded = value;\r\n    }\r\n  }\r\n\r\n  onSelectMenuItem(item: any, items: any[]) {\r\n    if (item?.subMenus?.length) {\r\n      items.forEach((o) => {\r\n        if (item.name !== o.name) o[\"expanded\"] = false;\r\n      });\r\n      item[\"expanded\"] = !item?.expanded;\r\n    } else if (item?.path) {\r\n      this.router.navigate([item?.path]);\r\n    }\r\n  }\r\n\r\n  buildNavList() {\r\n    const configNav = (navList: NavItem[]) =>\r\n      navList.reduce((list: NavItem[], item: NavItem) => {\r\n        if (item?.subMenus?.length) {\r\n          const children = configNav(item?.subMenus);\r\n          item.subMenus = children;\r\n          item.hasAccess = children.some((item: NavItem) => item.hasAccess);\r\n          item.class = item?.name\r\n            ?.toLowerCase()\r\n            ?.replace(/ & /g, \"-\")\r\n            ?.replace(/ /g, \"-\");\r\n        } else if (item?.acm?.length) {\r\n          item.hasAccess = this.acmService.hasACMAccess(item?.acm);\r\n        } else if (item?.roles?.length) {\r\n          item.hasAccess = this.acmService.hasRoleAccess(item?.roles);\r\n        } else {\r\n          item.hasAccess = item?.hasAccess ?? false;\r\n        }\r\n        delete item?.acm;\r\n        list.push(item);\r\n        return list;\r\n      }, []);\r\n    const navList = configNav(MENU_LIST());\r\n    this.navList = navList;\r\n  }\r\n\r\n  ngOnDestroy(): void {}\r\n\r\n  private loadProfileImage(profileImage: string) {\r\n    if (profileImage) {\r\n      this.userService.filePreview(profileImage).subscribe((res) => {\r\n        const mediaType = \"image/jpeg\";\r\n        const arrayBufferView = new Uint8Array(res);\r\n        const file = new Blob([arrayBufferView], { type: mediaType });\r\n        const myReader: FileReader = new FileReader();\r\n        myReader.onloadend = (e) => {\r\n          this.profileImage = myReader.result;\r\n        };\r\n        myReader.readAsDataURL(file);\r\n      });\r\n    }\r\n  }\r\n\r\n  private getLogoImage(user: any) {\r\n    if (!user?.id) return;\r\n    this.userService.getLogo().subscribe(\r\n      (res) => {\r\n        const mediaType = \"image/jpeg\";\r\n        const arrayBufferView = new Uint8Array(res);\r\n        const file = new Blob([arrayBufferView], { type: mediaType });\r\n        const myReader: FileReader = new FileReader();\r\n        myReader.onloadend = (e) => {\r\n          this.logoUrl = myReader.result;\r\n        };\r\n        myReader.readAsDataURL(file);\r\n      },\r\n      (err) => {\r\n        this.toastr.error(\"Logo is not found\");\r\n      }\r\n    );\r\n  }\r\n}\r\n\r\ninterface NavItem {\r\n  name: String;\r\n  icon?: String;\r\n  iconImage?: String;\r\n  path?: String;\r\n  subMenus?: NavItem[];\r\n  hide?: boolean;\r\n  acm?: ACMP[];\r\n  roles?: string[];\r\n  hasAccess?: boolean;\r\n  class?: string;\r\n}\r\n\r\nconst MENU_LIST = () => [\r\n  {\r\n    name: \"Account Search\",\r\n    icon: \"nav_dash_icon\",\r\n    path: \"/encollect/v1/dashboard\",\r\n    // hasAccess: true,\r\n    acm: [ACMP.CanSearchAccounts],\r\n  },\r\n  {\r\n    name: \"User Management\",\r\n    icon: \"nav_user_mgmt_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Agency Empanelment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Add Agency\",\r\n            path: \"/encollect/agency/v1/agencyEmpanelment-create\",\r\n            acm: [ACMP.CanCreateAgency],\r\n          },\r\n          {\r\n            name: \"Search Agency\",\r\n            path: \"/encollect/agency/v1/agencyEmpanelment-search\",\r\n            acm: [ACMP.CanSearchAgency],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Agent Empanelment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Add Agent\",\r\n            path: \"/encollect/agent/v1/create\",\r\n            acm: [ACMP.CanCreateAgent],\r\n          },\r\n          {\r\n            name: \"Search Agent\",\r\n            path: \"/encollect/agent/v1/search\",\r\n            acm: [ACMP.CanSearchAgent],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Staff Empanelment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Add Staff\",\r\n            path: \"/encollect/staff/v1/create-collection-staff\",\r\n            acm: [ACMP.CanCreateStaff],\r\n          },\r\n          {\r\n            name: \"Search Staff\",\r\n            path: \"/encollect/staff/v1/search-collection-staff\",\r\n            acm: [ACMP.CanSearchStaff],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Locked Profiles\",\r\n        path: \"/encollect/users/v1/locked-profiles\",\r\n        hide: true,\r\n        // acm: [ACM.UMLockedProfilesScreen], // TODO: ACM Configuration\r\n      },\r\n      {\r\n        name: \"Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"User Creation Upload\",\r\n            path: \"/encollect/users/v1/bulk-user-upload\",\r\n            acm: [ACMP.CanUploadBulkUser],\r\n          },\r\n          {\r\n            name: \"User Creation Upload Status\",\r\n            path: \"/encollect/users/v1/bulk-user-upload-status\",\r\n            acm: [ACMP.CanSearchBulkUserUploadStatus],\r\n          },\r\n          {\r\n            name: \"Bulk Enable/Disable Users\",\r\n            path: \"/encollect/users/v1/bulk-upload-enable-disable\",\r\n            acm: [ACMP.CanUploadBulkEnableDisableUser],\r\n          },\r\n          {\r\n            name: \"Bulk Enable/Disable Users Status\",\r\n            path: \"/encollect/users/v1/bulk-upload-enable-disable-status\",\r\n            acm: [ACMP.CanSearchBulkEnableDisableUserStatus],\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Allocation\",\r\n    icon: \"nav_allocation_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Agency Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agency Bulk Allocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-batch\",\r\n            acm: [ACMP.CanUploadPrimaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Bulk Allocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-batch-customerLevel\",\r\n            acm: [ACMP.CanUploadPrimaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Bulk Deallocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-unallocation-batch\",\r\n            acm: [ACMP.CanUploadPrimaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Bulk Deallocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-unallocation-batch-customerLevel\",\r\n            acm: [ACMP.CanUploadPrimaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Allocation Status\",\r\n            path: \"encollect/allocation/v1/primary-allocation-status\",\r\n            acm: [ACMP.CanSearchPrimaryAllocationBatchStatus],\r\n          },\r\n          {\r\n            name: \"Agency Deallocation Status\",\r\n            path: \"encollect/allocation/v1/primary-unallocation-status\",\r\n            acm: [ACMP.CanSearchPrimaryDeAllocationBatchStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Agent Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agent Bulk Allocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-allocation-batch\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Bulk Allocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-allocation-batch-customerLevel\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Bulk Deallocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-unallocation-batch\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Bulk Deallocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-unallocation-batch-customerLevel\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Allocation Status\",\r\n            path: \"encollect/allocation/v1/secondary-allocation-status\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanSearchSecondaryAllocationBatchStatus],\r\n          },\r\n          {\r\n            name: \"Agent Deallocation Status\",\r\n            path: \"encollect/allocation/v1/secondary-unallocation-status\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanSearchSecondaryDeAllocationBatchStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Allocation Owner Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Allocation Owner Bulk Upload Account Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-owner\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadAllocationOwnerBatch],\r\n          },\r\n          {\r\n            name: \"Allocation Owner Bulk Upload Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-owner-customerLevel\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadAllocationOwnerBatch],\r\n          },\r\n          {\r\n            name: \"Allocation Owner Upload Status\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-owner-status\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanSearchAllocationOwnerBatchStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Allocation Filters\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agency Allocation by Filters\",\r\n            path: \"encollect/allocation/v1/primary-allocation-filters\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUpdatePrimaryAllocationByFilter],\r\n          },\r\n          {\r\n            name: \"Agent Allocation by Filters\",\r\n            path: \"encollect/allocation/v1/secondary-allocation-filters\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUpdateSecondaryAllocationByFilter],\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Trails\",\r\n    icon: \"nav_trails_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Bulk Trail Upload\",\r\n        path: \"encollect/allocation/v1/bulk-trail\",\r\n        icon: \"fa fa-upload\",\r\n        acm: [ACMP.CanUploadBulkTrail],\r\n      },\r\n      {\r\n        name: \"Trail Upload Status\",\r\n        path: \"encollect/allocation/v1/bulk-trail-status\",\r\n        icon: \"fa fa-upload\",\r\n        acm: [ACMP.CanSearchBulkTrailStatus],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Payments\",\r\n    icon: \"nav_payments_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Receive Money from Collector\",\r\n        path: \"encollect/payments/v1/receive-money-from-collector\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanAcknowledgeReceipt],\r\n      },\r\n      {\r\n        name: \"Batch of Payments\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Batch of Payments\",\r\n            path: \"encollect/payments/v1/create-batch-of-payments\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreateBatch],\r\n          },\r\n          {\r\n            name: \"Search and Print Batch\",\r\n            path: \"encollect/payments/v1/print-batch-list\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSearchBatch, ACMP.CanPrintBatch],\r\n          },\r\n          {\r\n            name: \"Search and Edit Batch\",\r\n            path: \"encollect/payments/v1/search-and-edit-batch\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSearchBatch, ACMP.CanUpdateBatch],\r\n          },\r\n          {\r\n            name: \"Receive Batch of Payments at Branch\",\r\n            path: \"encollect/payments/v1/receive-batch-of-payments\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanAcknowledgeBatch],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Deposit Slip\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Deposit Slip\",\r\n            path: \"encollect/payments/v1/create-pay-slip\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreatePIS],\r\n          },\r\n          {\r\n            name: \"Search and View Deposit Slip\",\r\n            path: \"encollect/payments/v1/search-and-view-pay-in-slip\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSearchPIS],\r\n          },\r\n          {\r\n            name: \"Acknowledge Deposit Slip\",\r\n            path: \"encollect/payments/v1/central_ops_acknowledging\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanAcknowledgePIS],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Receipts\",\r\n        subMenus: [\r\n          {\r\n            name: \"Issue Receipt to Walk-in Customer\",\r\n            path: \"encollect/payments/v1/Walkin-customer-receipt\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreateWalkinReceipt],\r\n          },\r\n          {\r\n            name: \"Send Duplicate Receipt\",\r\n            path: \"encollect/payments/v1/search-and-send-duplicate-email-e-receipt-and-SMS\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSendDuplicateReceipt],\r\n          },\r\n          {\r\n            name: \"Raise Receipt Cancellation Request\",\r\n            path: \"encollect/payments/v1/reciept-cancellation-request\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreateReceiptCancellationRequest],\r\n          },\r\n          {\r\n            name: \"Action Receipt Cancellation Request\",\r\n            path: \"encollect/payments/v1/reciept-cancellation-request-approval-reject\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [\r\n              ACMP.CanApproveReceiptCancellationRequest,\r\n              ACMP.CanRejectReceiptCancellationRequest,\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Bulk Payments Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Bulk Payments Upload\",\r\n            path: \"encollect/payments/v1/bulk-payments\",\r\n            icon: \"fa fa-upload\",\r\n            acm: [ACMP.CanCreatePIS],\r\n          },\r\n          {\r\n            name: \"Bulk Payments Upload Status\",\r\n            path: \"encollect/payments/v1/bulk-payments-upload-status\",\r\n            icon: \"fa fa-upload\",\r\n            acm: [ACMP.CanCreatePIS],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Download Payment Report\",\r\n        path: \"encollect/payments/v1/download-payment-report\",\r\n        iconFa: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanDownloadPaymentReport],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Geo Report\",\r\n    icon: \"nav_geo_report_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"User Travel Report\",\r\n        path: \"travel-report/travel-report\",\r\n        icon: \"fa fa-users\",\r\n        acm: [ACMP.CanSearchTravelReport],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Digital ID\",\r\n    icon: \"nav_digital_id_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Digital ID Card\",\r\n        path: \"digital/card\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanViewDigitalIDCard],\r\n      },\r\n    ],\r\n  },\r\n   {\r\n    name: \"Settlement\",\r\n    icon: \"nav_settlement_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Find Eligible Cases\",\r\n        path: \"settlement/eligible-cases\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanFlagSettlementAsEligible],//CanViewDigitalIDCard\r\n      },\r\n       {\r\n        name: \"Request Settlement\",\r\n        path: \"settlement/request-settlement\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanRequestSettlement],\r\n      },\r\n       {\r\n        name: \"My Requests\",\r\n        path: \"settlement/my-settlement-summary\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanViewMySettlement],\r\n      },\r\n       {\r\n        name: \"My Action Queue\",\r\n        path: \"settlement/my-settlement-queue-summary\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanViewMyQueueSettlement],\r\n      },\r\n      {\r\n        name: \"Search Settlements\",\r\n        path: \"settlement/settlement-report\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanViewMySettlement],//CanViewSettlementReport\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Target Setting\", // TODO: Needs to remove if its not in use\r\n    icon: \"nav_target_setting_icon\",\r\n    hide: true,\r\n    subMenus: [\r\n      {\r\n        name: \"Upload Targets\",\r\n        path: \"target/upload-budgeted-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Upload Budgeted Target Status\",\r\n        path: \"target/budgeted-target-file-upload-status\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"View Budgeted Targets\",\r\n        path: \"target/view-budgeted-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Create Target\",\r\n        path: \"target/create-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Target Listing\",\r\n        path: \"target/search-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Curing Tools\",\r\n    icon: \"nav_curing_tools_icon\",\r\n    hide: true,\r\n    subMenus: [\r\n      {\r\n        name: \"Request Settlement\",\r\n        path: \"settlement/acs-request-settlement\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Requests\",\r\n        path: \"settlement/acs-mysettlement\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Settlement Queue\",\r\n        path: \"settlement/acs-settlement-queue\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Request Cure\",\r\n        path: \"encollect/cure/request-cure\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Cure\",\r\n        path: \"encollect/cure/my-cure\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Cure Queue\",\r\n        path: \"encollect/cure/my-cure-queue\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Add/Initiate Legal Case\",\r\n        path: \"encollect/legal-custom/create-legal\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Legal Case\",\r\n        path: \"encollect/legal-custom/my-legal\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Legal Queue\",\r\n        path: \"encollect/legal-custom/myqueue-legal\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Bulk Upload of Hearing Date\",\r\n        path: \"encollect/legal-custom/bulkupload-hearing\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Status of Bulk Upload of Hearing Date\",\r\n        path: \"encollect/legal-custom/bulkupload-hearing-status\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Bulk Upload to Initiate Case\",\r\n        path: \"encollect/legal-custom/bulkupload-initiate\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Status of Bulk Upload Initiate Case\",\r\n        path: \"encollect/legal-custom/bulkupload-initiate-status\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Initiate Legal Request\",\r\n        path: \"encollect/legal/initiate-legal-request\",\r\n        icon: \"fa fa-users\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"My Legal Queue\",\r\n        path: \"encollect/legal/my-legal-queue\",\r\n        icon: \"fa fa-users\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Create Repossession\",\r\n        path: \"encollect/repossession/create-repossession\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Repossession\",\r\n        path: \"encollect/repossession/my-repossession\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Queue Repossession\",\r\n        path: \"encollect/repossession/myqueue-repossession\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Segmentation & Treatment\",\r\n    icon: \"nav_segmentation_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Segmentation\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Segment\",\r\n            path: \"segmentation/create-segmentation\",\r\n            acm: [ACMP.CanCreateSegment],\r\n          },\r\n          {\r\n            name: \"Search Segments\",\r\n            path: \"segmentation/search-segmentation\",\r\n            acm: [ACMP.CanSearchSegment],\r\n          },\r\n          {\r\n            name: \"Sequence Segments\",\r\n            path: \"segmentation/segmentation-sequence\",\r\n            acm: [ACMP.CanSequenceSegment],\r\n          },\r\n          {\r\n            name: \"Compare Segments\",\r\n            path: \"segmentation/compare-segmentation\",\r\n            acm: [ACMP.CanCompareSegment],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Treatment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Treatment\",\r\n            path: \"treatment/create-treatment-step1\",\r\n            acm: [ACMP.CanCreateTreatment],\r\n          },\r\n          {\r\n            name: \"Search Treatments\",\r\n            path: \"treatment/search-treatment\",\r\n            acm: [ACMP.CanSearchTreatment],\r\n          },\r\n          {\r\n            name: \"Sequence Treatments\",\r\n            path: \"treatment/treatment-sequence\",\r\n            acm: [ACMP.CanSequenceTreatement],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Clear Segment / Treatment\",\r\n        path: \"segmentation/clear\",\r\n        acm: [ACMP.CanClearSegmentAndTreatementStamping],\r\n      },\r\n      {\r\n        name: \"Customer Search\",\r\n        path: \"coming-soon\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Account Search\",\r\n        path: \"coming-soon\",\r\n        hide: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"System Settings\",\r\n    icon: \"nav_system_settings_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Account Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Bulk Account Upload\",\r\n            path: \"settings/upload-account-import-master\",\r\n            acm: [ACMP.CanUploadBulkAccounts],\r\n          },\r\n          {\r\n            name: \"Account Upload Status\",\r\n            path: \"settings/upload-account-import-master-status\",\r\n            acm: [ACMP.CanSearchBulkAccountsUploadStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Masters Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Bulk Upload Masters\",\r\n            path: \"settings/bulk-upload-master\",\r\n            acm: [ACMP.CanUploadMasters],\r\n          },\r\n          {\r\n            name: \"Masters Upload Status\",\r\n            path: \"settings/bulk-upload-master-status\",\r\n            acm: [ACMP.CanSearchUploadMastersStatus],\r\n          },\r\n          {\r\n            name: \"View and Disable Masters\",\r\n            path: \"settings/view-masters\",\r\n            acm: [ACMP.CanSearchMasters], //SSViewDisableMastersScreen\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Permissions\",\r\n        subMenus: [\r\n          {\r\n            name: \"Define Permission Schemes\",\r\n            path: \"settings/permissions/define-permission-group\",\r\n            acm: [ACMP.CanCreatePermissionScheme],\r\n          },\r\n          {\r\n            name: \"Search Permission Schemes\",\r\n            path: \"settings/permissions/search-permission-groups\",\r\n            acm: [ACMP.CanViewPermissionSchemes],\r\n          },\r\n          {\r\n            name: \"Assign Permission Scheme to Designations\",\r\n            path: \"settings/permissions/assign-designations-to-permission-groups\",\r\n            acm: [ACMP.CanViewDesignationSchemeDetails],\r\n          },\r\n           {\r\n            name: \"Search Permissions\",\r\n            path: \"settings/permissions/search-permissions\",\r\n            acm: [ACMP.CanSearchPermissions],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Define ACM\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Web\",\r\n            path: \"settings/define-web-acm\",\r\n            acm: [ACMP.CanDefineACM],\r\n          },\r\n          {\r\n            name: \"Mobile\",\r\n            path: \"settings/define-mobile-acm\",\r\n            acm: [ACMP.CanDefineACM],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Basic UI Settings\",\r\n        path: \"settings/main-settings\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Disposition Code Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Create Disposition Code Group\",\r\n            path: \"settings/disposition-group-config\",\r\n          },\r\n          {\r\n            name: \"Create Disposition Code\",\r\n            path: \"settings/disposition-code-config\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Deposit Bank Account Number Config\",\r\n        path: \"settings/disposition-account-number\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Allocations\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Allocation Configuration\",\r\n            path: \"settings/allocation-config\",\r\n          },\r\n          {\r\n            name: \"Bucket Configuration\",\r\n            path: \"settings/allocation-bucket-config\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Payments\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Offline Receipts Configuration\",\r\n            path: \"settings/payments/offline-receipt\",\r\n          },\r\n          {\r\n            name: \"Transaction Series Configuration\",\r\n            path: \"settings/payments/transaction-series\",\r\n          },\r\n          {\r\n            name: \"Mode of Payments Configuration\",\r\n            path: \"settings/payments/mode-of-payments\",\r\n          },\r\n          {\r\n            name: \"Issue Receipt Masters Configuration\",\r\n            path: \"settings/payments/denominations\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Geography Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Geography Master\",\r\n            path: \"settings/upload-geography-master\",\r\n          },\r\n          {\r\n            name: \"Upload Geography Master Status\",\r\n            path: \"settings/upload-geography-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Geography Master\",\r\n            path: \"settings/geography-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Area Code Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Area Code Master\",\r\n            path: \"settings/upload-area-code-master\",\r\n          },\r\n          {\r\n            name: \"Upload Area Code Master Status\",\r\n            path: \"settings/upload-area-code-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Area Code Master\",\r\n            path: \"settings/area-code-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Bank Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Bank Master\",\r\n            path: \"settings/upload-bank-master\",\r\n          },\r\n          {\r\n            name: \"Upload Bank Master Status\",\r\n            path: \"settings/upload-bank-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Bank Master\",\r\n            path: \"settings/bank-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Account Detail Label Customization\",\r\n        path: \"settings/account-detail-label-customization\",\r\n        roles: [],\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Base Branch Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Base Branch Master\",\r\n            path: \"settings/upload-branch-master\",\r\n          },\r\n          {\r\n            name: \"Upload Base Branch Master Status\",\r\n            path: \"settings/upload-branch-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Base Branch Master\",\r\n            path: \"settings/branch-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Workflow\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Create Workflow\",\r\n            path: \"/workflows\",\r\n          },\r\n          {\r\n            name: \"Edit Workflow\",\r\n            path: \"/workflows/edit-workflow\",\r\n          },\r\n          {\r\n            name: \"View Workflow\",\r\n            path: \"/workflows/view-workflow\",\r\n          },\r\n          {\r\n            name: \"Search Workflow\",\r\n            path: \"/workflows/search-workflow\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Product Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Configure Product Group\",\r\n            path: \"settings/product-group-config\",\r\n          },\r\n          {\r\n            name: \"Configure Product\",\r\n            path: \"settings/product-config\",\r\n          },\r\n          {\r\n            name: \"Configure Sub Product\",\r\n            path: \"settings/sub-product-config\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Account Search Scope\",\r\n        path: \"settings/account-search-scope\",\r\n        roles: [\"SYSTEMADMIN\", \"BankToBackEndInternalBIHP\"],\r\n        // acm: [ACM.SSAccountSearchScopeScreen],\r\n        hide: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Communication\",\r\n    icon: \"nav_communication_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Search Communication Templates\",\r\n        path: \"communication/search-communication-templates\",\r\n        acm: [ACMP.CanSearchCommunicationTemplate],\r\n      },\r\n      {\r\n        name: \"Create Communication Template\",\r\n        path: \"communication/create-communication-template\",\r\n        acm: [ACMP.CanCreateCommunicationTemplate],\r\n      },\r\n      {\r\n        name: \"Search Communication Triggers\",\r\n        path: \"communication/search-communication-triggers\",\r\n        acm: [ACMP.CanSearchCommunicationTrigger],\r\n      },\r\n      {\r\n        name: \"Create Communication Trigger\",\r\n        path: \"communication/create-communication-trigger\",\r\n        acm: [ACMP.CanCreateCommunicationTrigger],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Reports\",\r\n    icon: \"nav_reports_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Allocation Reports\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agency Allocation Gap Report\",\r\n            path: \"reports/agency-allocation-gap\",\r\n            acm: [ACMP.CanViewAgencyAllocationGapReport],\r\n          },\r\n          {\r\n            name: \"Agent Allocation Gap Report\",\r\n            path: \"reports/agent-allocation-gap\",\r\n            acm: [ACMP.CanViewAgentAllocationGapReport],\r\n          },\r\n          {\r\n            name: \"Allocated vs Achieved Report\",\r\n            path: \"reports/allocated-vs-achieved\",\r\n            acm: [ACMP.CanViewAllocatedvsArchievedReport],\r\n          },\r\n          {\r\n            name: \"Agency Allocation Gap Report (Old)\",\r\n            path: \"reports/agency-gap-mis-report\",\r\n            acm: [ACMP.CanViewAgencyAllocationGapReport],\r\n          },\r\n          {\r\n            name: \"Agent Allocation Gap Report (Old)\",\r\n            path: \"reports/agent-gap-mis-report\",\r\n            acm: [ACMP.CanViewAgentAllocationGapReport],\r\n          },\r\n          {\r\n            name: \"Allocated vs Achieved Report (Old)\",\r\n            path: \"reports/allocated-vs-achieved-report\",\r\n            acm: [ACMP.CanViewAllocatedvsArchievedReport],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Trail Reports\",\r\n        subMenus: [\r\n           {\r\n            name: \"Trail Gap Report\",\r\n            path: \"reports/trail-gap-report\",\r\n            acm: [ACMP.CanViewTrailGapReport],\r\n          },\r\n          {\r\n            name: \"Trail Gap Report\",\r\n            path: \"reports/trail-gap-report\",\r\n            acm: [ACMP.CanViewTrailGapReport],\r\n          },\r\n          {\r\n            name: \"Trail History Report\",\r\n            path: \"reports/trail-history-report\",\r\n            acm: [ACMP.CanViewTrailHistoryReport],\r\n          },\r\n          {\r\n            name: \"Trail Intensity Report\",\r\n            path: \"reports/trail-intensity-report\",\r\n            acm: [ACMP.CanViewTrailIntensityReport],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Payment Report\",\r\n        path: \"reports/payment-report\",\r\n        acm: [ACMP.CanViewPaymentReport],\r\n      },\r\n      {\r\n        name: \"Money Movement Report\",\r\n        path: \"reports/money-movement-report\",\r\n        acm: [ACMP.CanViewMoneyMovementReport],\r\n      },\r\n      {\r\n        name: \"Attendance Report\",\r\n        path: \"attendance/attendance-report\",\r\n        acm: [ACMP.CanViewAttendanceReport],\r\n      },\r\n      {\r\n        name: \"Communication History Report\",\r\n        path: \"reports/communication-history-report\",\r\n        acm: [ACMP.CanViewCommunicationHistoryReport],\r\n      },\r\n      {\r\n        name: \"Account Dashboard Report\",\r\n        path: \"reports/account-dashboard-report\",\r\n        acm: [ACMP.CanViewAccountDashboardReport],\r\n      },\r\n      {\r\n        name: \"Performance Report\",\r\n        path: \"reports/performance-report\",\r\n        acm: [ACMP.CanViewPerformanceReport],\r\n      },\r\n      {\r\n        name: \"Supervisory Report\",\r\n        path: \"reports/supervisory-report\",\r\n        acm: [ACMP.CanViewSupervisoryReport],\r\n      },\r\n      {\r\n        name: \"Collection Intensity Report\",\r\n        path: \"reports/collection-intensity-report\",\r\n        acm: [ACMP.CanViewCollectionIntensityReport],\r\n      },\r\n      {\r\n        name: \"Collection Trend Report\",\r\n        path: \"reports/collection-trend-report\",\r\n        acm: [ACMP.CanViewCollectionTrendReport],\r\n      },\r\n      {\r\n        name: \"Visit Intensity Report\",\r\n        path: \"reports/visit-intensity-report\",\r\n        acm: [ACMP.CanViewVisitIntensityReport],\r\n      },\r\n      {\r\n        name: \"Collection Dashboard\",\r\n        path: \"reports/collection-dashboard\",\r\n        hide: false,\r\n      },\r\n      {\r\n        name: \"Allocation vs Collections vs Trails Analysis Report\",\r\n        path: \"reports/allocation-collection-trails-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Target vs Actual Report\",\r\n        path: \"reports/target-actual-analysis-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Daily Legal Report\",\r\n        path: \"reports/daily-legal-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Daily Repossession Report\",\r\n        path: \"reports/daily-repo-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Customer Contact Report\",\r\n        path: \"reports/ccd-report\",\r\n        acm: [ACMP.CanViewCustomerContactReport],\r\n      },\r\n      {\r\n        name: \"Cash Wallet Limit Report\",\r\n        path: \"reports/cash-wallet-limit-report\",\r\n        acm: [ACMP.CanViewCashWalletLimitReport], //CanViewCashWalletLimitReport\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Insights\",\r\n    icon: \"nav_insights_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Primary Allocation Insights\",\r\n        path: \"insights/primary-allocation-insights\",\r\n        acm: [ACMP.CanViewAgencyAllocationGapReport],\r\n      },\r\n      {\r\n        name: \"Secondary Allocation Insights\",\r\n        path: \"insights/secondary-allocation-insights\",\r\n        acm: [ACMP.CanViewAgentAllocationGapReport],\r\n      },\r\n      {\r\n        name: \"Trail Gap Insights\",\r\n        path: \"insights/trail-gap-insights\",\r\n        acm: [ACMP.CanViewTrailGapReport],\r\n      },\r\n      {\r\n        name: \"Allocated vs Achieved Insights\",\r\n        path: \"insights/allocated-vs-achieved-insights\",\r\n        acm: [ACMP.CanViewAllocatedvsArchievedReport],\r\n      },\r\n      {\r\n        name: \"Money Movement Insights (Agency Staff)\",\r\n        path: \"insights/money-movement-insights-agency-staff\",\r\n        acm: [ACMP.CanViewMoneyMovementReport],\r\n      },\r\n      {\r\n        name: \"Money Movement Insights (Bank Staff)\",\r\n        path: \"insights/money-movement-insights-bank-staff\",\r\n        acm: [ACMP.CanViewMoneyMovementReport],\r\n      },\r\n    ],\r\n  },\r\n];\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAmB,eAAe;AAC5D,SACEC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,eAAe,EACfC,MAAM,EACNC,UAAU,EACVC,gBAAgB,QACX,iBAAiB;AACxB,SAASC,QAAQ,EAAEC,GAAG,QAAoB,MAAM;AAChD,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,IAAI,EAAEC,UAAU,QAAQ,gBAAgB;AACjD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SACEC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,gBAAgB,QACX,iBAAiB;AACxB,SAASC,iBAAiB,QAAQ,eAAe;AACjD,SAASC,gBAAgB,QAAQ,wBAAwB;AAGzD,MAAMC,YAAY,GACfC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAkB,IAAI,MAAM;AAkB3D,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAmB9BC,YACUC,MAAc,EACdC,WAAwB,EACxBC,UAAsB,EACtBC,MAAqB;IAHrB,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IAtBR,KAAAC,eAAe,GAAG9B,MAAM,CAACc,eAAe,CAAC;IAE1C,KAAAiB,YAAY,GAAGvB,QAAQ,CAAC,IAAI,CAAC,CAACwB,IAAI,CAACvB,GAAG,CAAC,MAAM,IAAIwB,IAAI,EAAE,CAAC,CAAC;IAIzD,KAAAC,SAAS,GAAY,KAAK;IAI1B,KAAAC,iBAAiB,GAAYd,YAAY,KAAK,MAAM;IACpD,KAAAA,YAAY,GAAiBA,YAAY;IACzC,KAAAe,YAAY,GAED,4xDAA4xD,CAAC,CAAC;IACzyD,KAAAC,OAAO,GAAyB,EAAE;IAEzC,KAAAC,WAAW,GAAG,EAAE;IAOd,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACZ,WAAW,CAACa,eAAe;IACxD,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACF,gBAAgB,CAACG,SAAS,CAAEC,SAAkB,IAAI;MACrD,IAAIA,SAAS,EAAE;QACb,IAAI,CAACC,YAAY,EAAE;MACrB;MACA,IAAI,CAACN,WAAW,GAAG,IAAI,CAACR,eAAe,CAACe,cAAc,EAAE;IAC1D,CAAC,CAAC;IACF,IAAI,CAACnB,MAAM,CAACoB,MAAM,CAACJ,SAAS,CAAEK,KAAK,IAAI;MACrC,IAAIA,KAAK,YAAY3C,eAAe,EAAE;QACpC,IAAI,CAAC8B,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IACLa,KAAK,YAAY7C,aAAa,IAC9B6C,KAAK,YAAY9C,gBAAgB,IACjC8C,KAAK,YAAY5C,eAAe,EAChC;QACA,IAAI,CAAC+B,SAAS,GAAG,KAAK;MACxB;IACF,CAAC,CAAC;EACJ;EAEQO,gBAAgBA,CAAA;IACtB,IAAI,CAACd,WAAW,CAACqB,WAAW,CAACN,SAAS,CAAEO,QAAQ,IAAS;MACvD,IAAI,CAACD,WAAW,GAAGC,QAAQ;MAC3B,IAAI,CAACC,WAAW,GACd,IAAI,CAACF,WAAW,EAAEG,KAAK,EAAEC,IAAI,CAAEC,CAAC,IAAKA,CAAC,EAAEC,oBAAoB,CAAC,IAC7D,IAAI,CAACN,WAAW,EAAEG,KAAK,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACI,gBAAgB,CAACN,QAAQ,EAAEb,YAAY,CAAC;MAC7C,IAAI,CAACoB,YAAY,CAACP,QAAQ,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAQ,aAAaA,CAAA;IACX,IAAI,CAACpC,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;IAClE,IAAI,CAACc,iBAAiB,GAAG,IAAI,CAACd,YAAY,KAAK,MAAM;IACrDC,YAAY,CAACoC,OAAO,CAAC,cAAc,EAAE,GAAG,IAAI,CAACrC,YAAY,EAAE,CAAC;EAC9D;EAEAsC,YAAYA,CAACC,KAAc;IACzB,IAAI,IAAI,CAACvC,YAAY,KAAK,MAAM,EAAE;MAChC,IAAI,CAACc,iBAAiB,GAAGyB,KAAK;IAChC;EACF;EAEAC,gBAAgBA,CAACC,IAAS,EAAEC,KAAY;IACtC,IAAID,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAE;MAC1BF,KAAK,CAACG,OAAO,CAAEb,CAAC,IAAI;QAClB,IAAIS,IAAI,CAACK,IAAI,KAAKd,CAAC,CAACc,IAAI,EAAEd,CAAC,CAAC,UAAU,CAAC,GAAG,KAAK;MACjD,CAAC,CAAC;MACFS,IAAI,CAAC,UAAU,CAAC,GAAG,CAACA,IAAI,EAAEM,QAAQ;IACpC,CAAC,MAAM,IAAIN,IAAI,EAAEO,IAAI,EAAE;MACrB,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAACR,IAAI,EAAEO,IAAI,CAAC,CAAC;IACpC;EACF;EAEAzB,YAAYA,CAAA;IACV,MAAM2B,SAAS,GAAIC,OAAkB,IACnCA,OAAO,CAACC,MAAM,CAAC,CAACC,IAAe,EAAEZ,IAAa,KAAI;MAChD,IAAIA,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAE;QAC1B,MAAMU,QAAQ,GAAGJ,SAAS,CAACT,IAAI,EAAEE,QAAQ,CAAC;QAC1CF,IAAI,CAACE,QAAQ,GAAGW,QAAQ;QACxBb,IAAI,CAACc,SAAS,GAAGD,QAAQ,CAACE,IAAI,CAAEf,IAAa,IAAKA,IAAI,CAACc,SAAS,CAAC;QACjEd,IAAI,CAACgB,KAAK,GAAGhB,IAAI,EAAEK,IAAI,EACnBY,WAAW,EAAE,EACbC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EACpBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MACxB,CAAC,MAAM,IAAIlB,IAAI,EAAEmB,GAAG,EAAEhB,MAAM,EAAE;QAC5BH,IAAI,CAACc,SAAS,GAAG,IAAI,CAAChD,UAAU,CAACsD,YAAY,CAACpB,IAAI,EAAEmB,GAAG,CAAC;MAC1D,CAAC,MAAM,IAAInB,IAAI,EAAEX,KAAK,EAAEc,MAAM,EAAE;QAC9BH,IAAI,CAACc,SAAS,GAAG,IAAI,CAAChD,UAAU,CAACuD,aAAa,CAACrB,IAAI,EAAEX,KAAK,CAAC;MAC7D,CAAC,MAAM;QACLW,IAAI,CAACc,SAAS,GAAGd,IAAI,EAAEc,SAAS,IAAI,KAAK;MAC3C;MACA,OAAOd,IAAI,EAAEmB,GAAG;MAChBP,IAAI,CAACU,IAAI,CAACtB,IAAI,CAAC;MACf,OAAOY,IAAI;IACb,CAAC,EAAE,EAAE,CAAC;IACR,MAAMF,OAAO,GAAGD,SAAS,CAACc,SAAS,EAAE,CAAC;IACtC,IAAI,CAACb,OAAO,GAAGA,OAAO;EACxB;EAEAc,WAAWA,CAAA,GAAU;EAEb/B,gBAAgBA,CAACnB,YAAoB;IAC3C,IAAIA,YAAY,EAAE;MAChB,IAAI,CAACT,WAAW,CAAC4D,WAAW,CAACnD,YAAY,CAAC,CAACM,SAAS,CAAE8C,GAAG,IAAI;QAC3D,MAAMC,SAAS,GAAG,YAAY;QAC9B,MAAMC,eAAe,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;QAC3C,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,eAAe,CAAC,EAAE;UAAEI,IAAI,EAAEL;QAAS,CAAE,CAAC;QAC7D,MAAMM,QAAQ,GAAe,IAAIC,UAAU,EAAE;QAC7CD,QAAQ,CAACE,SAAS,GAAIC,CAAC,IAAI;UACzB,IAAI,CAAC9D,YAAY,GAAG2D,QAAQ,CAACI,MAAM;QACrC,CAAC;QACDJ,QAAQ,CAACK,aAAa,CAACR,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ;EACF;EAEQpC,YAAYA,CAAC6C,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAEC,EAAE,EAAE;IACf,IAAI,CAAC3E,WAAW,CAAC4E,OAAO,EAAE,CAAC7D,SAAS,CACjC8C,GAAG,IAAI;MACN,MAAMC,SAAS,GAAG,YAAY;MAC9B,MAAMC,eAAe,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;MAC3C,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,eAAe,CAAC,EAAE;QAAEI,IAAI,EAAEL;MAAS,CAAE,CAAC;MAC7D,MAAMM,QAAQ,GAAe,IAAIC,UAAU,EAAE;MAC7CD,QAAQ,CAACE,SAAS,GAAIC,CAAC,IAAI;QACzB,IAAI,CAAC7D,OAAO,GAAG0D,QAAQ,CAACI,MAAM;MAChC,CAAC;MACDJ,QAAQ,CAACK,aAAa,CAACR,IAAI,CAAC;IAC9B,CAAC,EACAY,GAAG,IAAI;MACN,IAAI,CAAC3E,MAAM,CAAC4E,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC,CACF;EACH;;;;;;;;;;;;;AA5IWjF,mBAAmB,GAAAkF,UAAA,EAhB/B3G,SAAS,CAAC;EACT4G,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP9F,SAAS,EACTC,QAAQ,EACRV,UAAU,EACVC,gBAAgB,EAChBU,OAAO,EACPC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,CACjB;EACD0F,QAAA,EAAAC,oBAA2C;;CAE5C,CAAC,C,EACWvF,mBAAmB,CA6I/B;;AAeD,MAAM6D,SAAS,GAAGA,CAAA,KAAM,CACtB;EACElB,IAAI,EAAE,gBAAgB;EACtB6C,IAAI,EAAE,eAAe;EACrB3C,IAAI,EAAE,yBAAyB;EAC/B;EACAY,GAAG,EAAE,CAACtE,IAAI,CAACsG,iBAAiB;CAC7B,EACD;EACE9C,IAAI,EAAE,iBAAiB;EACvB6C,IAAI,EAAE,oBAAoB;EAC1BhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,YAAY;MAClBE,IAAI,EAAE,+CAA+C;MACrDY,GAAG,EAAE,CAACtE,IAAI,CAACuG,eAAe;KAC3B,EACD;MACE/C,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE,+CAA+C;MACrDY,GAAG,EAAE,CAACtE,IAAI,CAACwG,eAAe;KAC3B;GAEJ,EACD;IACEhD,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,WAAW;MACjBE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAACtE,IAAI,CAACyG,cAAc;KAC1B,EACD;MACEjD,IAAI,EAAE,cAAc;MACpBE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAACtE,IAAI,CAAC0G,cAAc;KAC1B;GAEJ,EACD;IACElD,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,WAAW;MACjBE,IAAI,EAAE,6CAA6C;MACnDY,GAAG,EAAE,CAACtE,IAAI,CAAC2G,cAAc;KAC1B,EACD;MACEnD,IAAI,EAAE,cAAc;MACpBE,IAAI,EAAE,6CAA6C;MACnDY,GAAG,EAAE,CAACtE,IAAI,CAAC4G,cAAc;KAC1B;GAEJ,EACD;IACEpD,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,qCAAqC;IAC3CmD,IAAI,EAAE;IACN;GACD,EACD;IACErD,IAAI,EAAE,aAAa;IACnBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE,sCAAsC;MAC5CY,GAAG,EAAE,CAACtE,IAAI,CAAC8G,iBAAiB;KAC7B,EACD;MACEtD,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,6CAA6C;MACnDY,GAAG,EAAE,CAACtE,IAAI,CAAC+G,6BAA6B;KACzC,EACD;MACEvD,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,gDAAgD;MACtDY,GAAG,EAAE,CAACtE,IAAI,CAACgH,8BAA8B;KAC1C,EACD;MACExD,IAAI,EAAE,kCAAkC;MACxCE,IAAI,EAAE,uDAAuD;MAC7DY,GAAG,EAAE,CAACtE,IAAI,CAACiH,oCAAoC;KAChD;GAEJ;CAEJ,EACD;EACEzD,IAAI,EAAE,YAAY;EAClB6C,IAAI,EAAE,qBAAqB;EAC3BhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,sCAAsC;MAC5CE,IAAI,EAAE,wDAAwD;MAC9DY,GAAG,EAAE,CAACtE,IAAI,CAACkH,+BAA+B;KAC3C,EACD;MACE1D,IAAI,EAAE,uCAAuC;MAC7CE,IAAI,EAAE,sEAAsE;MAC5EY,GAAG,EAAE,CAACtE,IAAI,CAACkH,+BAA+B;KAC3C,EACD;MACE1D,IAAI,EAAE,wCAAwC;MAC9CE,IAAI,EAAE,0DAA0D;MAChEY,GAAG,EAAE,CAACtE,IAAI,CAACmH,iCAAiC;KAC7C,EACD;MACE3D,IAAI,EAAE,yCAAyC;MAC/CE,IAAI,EAAE,wEAAwE;MAC9EY,GAAG,EAAE,CAACtE,IAAI,CAACmH,iCAAiC;KAC7C,EACD;MACE3D,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,mDAAmD;MACzDY,GAAG,EAAE,CAACtE,IAAI,CAACoH,qCAAqC;KACjD,EACD;MACE5D,IAAI,EAAE,4BAA4B;MAClCE,IAAI,EAAE,qDAAqD;MAC3DY,GAAG,EAAE,CAACtE,IAAI,CAACqH,uCAAuC;KACnD;GAEJ,EACD;IACE7D,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE,2DAA2D;MACjE2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAACsH,iCAAiC;KAC7C,EACD;MACE9D,IAAI,EAAE,sCAAsC;MAC5CE,IAAI,EAAE,yEAAyE;MAC/E2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAACsH,iCAAiC;KAC7C,EACD;MACE9D,IAAI,EAAE,uCAAuC;MAC7CE,IAAI,EAAE,6DAA6D;MACnE2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAACuH,mCAAmC;KAC/C,EACD;MACE/D,IAAI,EAAE,wCAAwC;MAC9CE,IAAI,EAAE,2EAA2E;MACjF2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAACuH,mCAAmC;KAC/C,EACD;MACE/D,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE,qDAAqD;MAC3D2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAACwH,uCAAuC;KACnD,EACD;MACEhE,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,uDAAuD;MAC7D2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAACyH,yCAAyC;KACrD;GAEJ,EACD;IACEjE,IAAI,EAAE,8BAA8B;IACpCH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,4CAA4C;MAClDE,IAAI,EAAE,wDAAwD;MAC9D2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAAC0H,6BAA6B;KACzC,EACD;MACElE,IAAI,EAAE,6CAA6C;MACnDE,IAAI,EAAE,sEAAsE;MAC5E2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAAC0H,6BAA6B;KACzC,EACD;MACElE,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE,+DAA+D;MACrE2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAAC2H,mCAAmC;KAC/C;GAEJ,EACD;IACEnE,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,oDAAoD;MAC1D2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAAC4H,kCAAkC;KAC9C,EACD;MACEpE,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,sDAAsD;MAC5D2C,IAAI,EAAE,iBAAiB;MACvB/B,GAAG,EAAE,CAACtE,IAAI,CAAC6H,oCAAoC;KAChD;GAEJ;CAEJ,EACD;EACErE,IAAI,EAAE,QAAQ;EACd6C,IAAI,EAAE,iBAAiB;EACvBhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,mBAAmB;IACzBE,IAAI,EAAE,oCAAoC;IAC1C2C,IAAI,EAAE,cAAc;IACpB/B,GAAG,EAAE,CAACtE,IAAI,CAAC8H,kBAAkB;GAC9B,EACD;IACEtE,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,2CAA2C;IACjD2C,IAAI,EAAE,cAAc;IACpB/B,GAAG,EAAE,CAACtE,IAAI,CAAC+H,wBAAwB;GACpC;CAEJ,EACD;EACEvE,IAAI,EAAE,UAAU;EAChB6C,IAAI,EAAE,mBAAmB;EACzBhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,oDAAoD;IAC1D2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACgI,qBAAqB;GACjC,EACD;IACExE,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,gDAAgD;MACtD2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACiI,cAAc;KAC1B,EACD;MACEzE,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,wCAAwC;MAC9C2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACkI,cAAc,EAAElI,IAAI,CAACmI,aAAa;KAC9C,EACD;MACE3E,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE,6CAA6C;MACnD2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACkI,cAAc,EAAElI,IAAI,CAACoI,cAAc;KAC/C,EACD;MACE5E,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE,iDAAiD;MACvD2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACqI,mBAAmB;KAC/B;GAEJ,EACD;IACE7E,IAAI,EAAE,cAAc;IACpBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,uCAAuC;MAC7C2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACsI,YAAY;KACxB,EACD;MACE9E,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,mDAAmD;MACzD2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACuI,YAAY;KACxB,EACD;MACE/E,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,iDAAiD;MACvD2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACwI,iBAAiB;KAC7B;GAEJ,EACD;IACEhF,IAAI,EAAE,UAAU;IAChBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,mCAAmC;MACzCE,IAAI,EAAE,+CAA+C;MACrD2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACyI,sBAAsB;KAClC,EACD;MACEjF,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,yEAAyE;MAC/E2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAAC0I,uBAAuB;KACnC,EACD;MACElF,IAAI,EAAE,oCAAoC;MAC1CE,IAAI,EAAE,oDAAoD;MAC1D2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CAACtE,IAAI,CAAC2I,mCAAmC;KAC/C,EACD;MACEnF,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE,oEAAoE;MAC1E2C,IAAI,EAAE,mBAAmB;MACzB/B,GAAG,EAAE,CACHtE,IAAI,CAAC4I,oCAAoC,EACzC5I,IAAI,CAAC6I,mCAAmC;KAE3C;GAEJ,EACD;IACErF,IAAI,EAAE,sBAAsB;IAC5BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE,qCAAqC;MAC3C2C,IAAI,EAAE,cAAc;MACpB/B,GAAG,EAAE,CAACtE,IAAI,CAACsI,YAAY;KACxB,EACD;MACE9E,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,mDAAmD;MACzD2C,IAAI,EAAE,cAAc;MACpB/B,GAAG,EAAE,CAACtE,IAAI,CAACsI,YAAY;KACxB;GAEJ,EACD;IACE9E,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,+CAA+C;IACrDoF,MAAM,EAAE,mBAAmB;IAC3BxE,GAAG,EAAE,CAACtE,IAAI,CAAC+I,wBAAwB;GACpC;CAEJ,EACD;EACEvF,IAAI,EAAE,YAAY;EAClB6C,IAAI,EAAE,qBAAqB;EAC3BhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,6BAA6B;IACnC2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE,CAACtE,IAAI,CAACgJ,qBAAqB;GACjC;CAEJ,EACD;EACExF,IAAI,EAAE,YAAY;EAClB6C,IAAI,EAAE,qBAAqB;EAC3BhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,cAAc;IACpB2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACiJ,oBAAoB;GAChC;CAEJ,EACA;EACCzF,IAAI,EAAE,YAAY;EAClB6C,IAAI,EAAE,qBAAqB;EAC3BhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,2BAA2B;IACjC2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACkJ,2BAA2B,CAAC,CAAC;GACzC,EACA;IACC1F,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,+BAA+B;IACrC2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACmJ,oBAAoB;GAChC,EACA;IACC3F,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE,kCAAkC;IACxC2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACoJ,mBAAmB;GAC/B,EACA;IACC5F,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,wCAAwC;IAC9C2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACqJ,wBAAwB;GACpC,EACD;IACE7F,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,8BAA8B;IACpC2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE,CAACtE,IAAI,CAACoJ,mBAAmB,CAAC,CAAC;GACjC;CAEJ,EACD;EACE5F,IAAI,EAAE,gBAAgB;EAAE;EACxB6C,IAAI,EAAE,yBAAyB;EAC/BQ,IAAI,EAAE,IAAI;EACVxD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,+BAA+B;IACrC2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,2CAA2C;IACjD2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,uBAAuB;IAC7BE,IAAI,EAAE,6BAA6B;IACnC2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE,sBAAsB;IAC5B2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,sBAAsB;IAC5B2C,IAAI,EAAE,mBAAmB;IACzB/B,GAAG,EAAE;GACN;CAEJ,EACD;EACEd,IAAI,EAAE,cAAc;EACpB6C,IAAI,EAAE,uBAAuB;EAC7BQ,IAAI,EAAE,IAAI;EACVxD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,mCAAmC;IACzC2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE,6BAA6B;IACnC2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,iCAAiC;IACvC2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,cAAc;IACpBE,IAAI,EAAE,6BAA6B;IACnC2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,SAAS;IACfE,IAAI,EAAE,wBAAwB;IAC9B2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE,8BAA8B;IACpC2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,qCAAqC;IAC3C2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE,iCAAiC;IACvC2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,sCAAsC;IAC5C2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,6BAA6B;IACnCE,IAAI,EAAE,2CAA2C;IACjD2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,uCAAuC;IAC7CE,IAAI,EAAE,kDAAkD;IACxD2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,4CAA4C;IAClD2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,qCAAqC;IAC3CE,IAAI,EAAE,mDAAmD;IACzD2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,wBAAwB;IAC9BE,IAAI,EAAE,wCAAwC;IAC9C2C,IAAI,EAAE,aAAa;IACnBQ,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,gCAAgC;IACtC2C,IAAI,EAAE,aAAa;IACnBQ,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,4CAA4C;IAClD2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,wCAAwC;IAC9C2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,uBAAuB;IAC7BE,IAAI,EAAE,6CAA6C;IACnD2C,IAAI,EAAE,aAAa;IACnB/B,GAAG,EAAE;GACN;CAEJ,EACD;EACEd,IAAI,EAAE,0BAA0B;EAChC6C,IAAI,EAAE,uBAAuB;EAC7BhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,cAAc;IACpBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,gBAAgB;MACtBE,IAAI,EAAE,kCAAkC;MACxCY,GAAG,EAAE,CAACtE,IAAI,CAACsJ,gBAAgB;KAC5B,EACD;MACE9F,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE,kCAAkC;MACxCY,GAAG,EAAE,CAACtE,IAAI,CAACuJ,gBAAgB;KAC5B,EACD;MACE/F,IAAI,EAAE,mBAAmB;MACzBE,IAAI,EAAE,oCAAoC;MAC1CY,GAAG,EAAE,CAACtE,IAAI,CAACwJ,kBAAkB;KAC9B,EACD;MACEhG,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE,mCAAmC;MACzCY,GAAG,EAAE,CAACtE,IAAI,CAACyJ,iBAAiB;KAC7B;GAEJ,EACD;IACEjG,IAAI,EAAE,WAAW;IACjBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE,kCAAkC;MACxCY,GAAG,EAAE,CAACtE,IAAI,CAAC0J,kBAAkB;KAC9B,EACD;MACElG,IAAI,EAAE,mBAAmB;MACzBE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAACtE,IAAI,CAAC2J,kBAAkB;KAC9B,EACD;MACEnG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,8BAA8B;MACpCY,GAAG,EAAE,CAACtE,IAAI,CAAC4J,qBAAqB;KACjC;GAEJ,EACD;IACEpG,IAAI,EAAE,2BAA2B;IACjCE,IAAI,EAAE,oBAAoB;IAC1BY,GAAG,EAAE,CAACtE,IAAI,CAAC6J,oCAAoC;GAChD,EACD;IACErG,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,aAAa;IACnBmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,aAAa;IACnBmD,IAAI,EAAE;GACP;CAEJ,EACD;EACErD,IAAI,EAAE,iBAAiB;EACvB6C,IAAI,EAAE,0BAA0B;EAChChD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,gBAAgB;IACtBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,uCAAuC;MAC7CY,GAAG,EAAE,CAACtE,IAAI,CAAC8J,qBAAqB;KACjC,EACD;MACEtG,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE,8CAA8C;MACpDY,GAAG,EAAE,CAACtE,IAAI,CAAC+J,iCAAiC;KAC7C;GAEJ,EACD;IACEvG,IAAI,EAAE,gBAAgB;IACtBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,6BAA6B;MACnCY,GAAG,EAAE,CAACtE,IAAI,CAACgK,gBAAgB;KAC5B,EACD;MACExG,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE,oCAAoC;MAC1CY,GAAG,EAAE,CAACtE,IAAI,CAACiK,4BAA4B;KACxC,EACD;MACEzG,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,uBAAuB;MAC7BY,GAAG,EAAE,CAACtE,IAAI,CAACkK,gBAAgB,CAAC,CAAE;KAC/B;GAEJ,EACD;IACE1G,IAAI,EAAE,aAAa;IACnBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,8CAA8C;MACpDY,GAAG,EAAE,CAACtE,IAAI,CAACmK,yBAAyB;KACrC,EACD;MACE3G,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,+CAA+C;MACrDY,GAAG,EAAE,CAACtE,IAAI,CAACoK,wBAAwB;KACpC,EACD;MACE5G,IAAI,EAAE,0CAA0C;MAChDE,IAAI,EAAE,+DAA+D;MACrEY,GAAG,EAAE,CAACtE,IAAI,CAACqK,+BAA+B;KAC3C,EACA;MACC7G,IAAI,EAAE,oBAAoB;MAC1BE,IAAI,EAAE,yCAAyC;MAC/CY,GAAG,EAAE,CAACtE,IAAI,CAACsK,oBAAoB;KAChC;GAEJ,EACD;IACE9G,IAAI,EAAE,YAAY;IAClBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,KAAK;MACXE,IAAI,EAAE,yBAAyB;MAC/BY,GAAG,EAAE,CAACtE,IAAI,CAACuK,YAAY;KACxB,EACD;MACE/G,IAAI,EAAE,QAAQ;MACdE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAACtE,IAAI,CAACuK,YAAY;KACxB;GAEJ,EACD;IACE/G,IAAI,EAAE,mBAAmB;IACzBE,IAAI,EAAE,wBAAwB;IAC9BmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,yBAAyB;IAC/BqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,+BAA+B;MACrCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,oCAAoC;IAC1CE,IAAI,EAAE,qCAAqC;IAC3CmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,aAAa;IACnBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,UAAU;IAChBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,kCAAkC;MACxCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,aAAa;IACnBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,oBAAoB;MAC1BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,oBAAoB;MAC1BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,oCAAoC;IAC1CE,IAAI,EAAE,6CAA6C;IACnDlB,KAAK,EAAE,EAAE;IACTqE,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,oBAAoB;IAC1BqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,kCAAkC;MACxCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,UAAU;IAChBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,gBAAgB;IACtBqD,IAAI,EAAE,IAAI;IACVxD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,mBAAmB;MACzBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,sBAAsB;IAC5BE,IAAI,EAAE,+BAA+B;IACrClB,KAAK,EAAE,CAAC,aAAa,EAAE,2BAA2B,CAAC;IACnD;IACAqE,IAAI,EAAE;GACP;CAEJ,EACD;EACErD,IAAI,EAAE,eAAe;EACrB6C,IAAI,EAAE,wBAAwB;EAC9BhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,gCAAgC;IACtCE,IAAI,EAAE,8CAA8C;IACpDY,GAAG,EAAE,CAACtE,IAAI,CAACwK,8BAA8B;GAC1C,EACD;IACEhH,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,6CAA6C;IACnDY,GAAG,EAAE,CAACtE,IAAI,CAACyK,8BAA8B;GAC1C,EACD;IACEjH,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,6CAA6C;IACnDY,GAAG,EAAE,CAACtE,IAAI,CAAC0K,6BAA6B;GACzC,EACD;IACElH,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,4CAA4C;IAClDY,GAAG,EAAE,CAACtE,IAAI,CAAC2K,6BAA6B;GACzC;CAEJ,EACD;EACEnH,IAAI,EAAE,SAAS;EACf6C,IAAI,EAAE,kBAAkB;EACxBhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,+BAA+B;MACrCY,GAAG,EAAE,CAACtE,IAAI,CAAC4K,gCAAgC;KAC5C,EACD;MACEpH,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,8BAA8B;MACpCY,GAAG,EAAE,CAACtE,IAAI,CAAC6K,+BAA+B;KAC3C,EACD;MACErH,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,+BAA+B;MACrCY,GAAG,EAAE,CAACtE,IAAI,CAAC8K,iCAAiC;KAC7C,EACD;MACEtH,IAAI,EAAE,oCAAoC;MAC1CE,IAAI,EAAE,+BAA+B;MACrCY,GAAG,EAAE,CAACtE,IAAI,CAAC4K,gCAAgC;KAC5C,EACD;MACEpH,IAAI,EAAE,mCAAmC;MACzCE,IAAI,EAAE,8BAA8B;MACpCY,GAAG,EAAE,CAACtE,IAAI,CAAC6K,+BAA+B;KAC3C,EACD;MACErH,IAAI,EAAE,oCAAoC;MAC1CE,IAAI,EAAE,sCAAsC;MAC5CY,GAAG,EAAE,CAACtE,IAAI,CAAC8K,iCAAiC;KAC7C;GAEJ,EACD;IACEtH,IAAI,EAAE,eAAe;IACrBH,QAAQ,EAAE,CACP;MACCG,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE,0BAA0B;MAChCY,GAAG,EAAE,CAACtE,IAAI,CAAC+K,qBAAqB;KACjC,EACD;MACEvH,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE,0BAA0B;MAChCY,GAAG,EAAE,CAACtE,IAAI,CAAC+K,qBAAqB;KACjC,EACD;MACEvH,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE,8BAA8B;MACpCY,GAAG,EAAE,CAACtE,IAAI,CAACgL,yBAAyB;KACrC,EACD;MACExH,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,gCAAgC;MACtCY,GAAG,EAAE,CAACtE,IAAI,CAACiL,2BAA2B;KACvC;GAEJ,EACD;IACEzH,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,wBAAwB;IAC9BY,GAAG,EAAE,CAACtE,IAAI,CAACkL,oBAAoB;GAChC,EACD;IACE1H,IAAI,EAAE,uBAAuB;IAC7BE,IAAI,EAAE,+BAA+B;IACrCY,GAAG,EAAE,CAACtE,IAAI,CAACmL,0BAA0B;GACtC,EACD;IACE3H,IAAI,EAAE,mBAAmB;IACzBE,IAAI,EAAE,8BAA8B;IACpCY,GAAG,EAAE,CAACtE,IAAI,CAACoL,uBAAuB;GACnC,EACD;IACE5H,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,sCAAsC;IAC5CY,GAAG,EAAE,CAACtE,IAAI,CAACqL,iCAAiC;GAC7C,EACD;IACE7H,IAAI,EAAE,0BAA0B;IAChCE,IAAI,EAAE,kCAAkC;IACxCY,GAAG,EAAE,CAACtE,IAAI,CAACsL,6BAA6B;GACzC,EACD;IACE9H,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,4BAA4B;IAClCY,GAAG,EAAE,CAACtE,IAAI,CAACuL,wBAAwB;GACpC,EACD;IACE/H,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,4BAA4B;IAClCY,GAAG,EAAE,CAACtE,IAAI,CAACwL,wBAAwB;GACpC,EACD;IACEhI,IAAI,EAAE,6BAA6B;IACnCE,IAAI,EAAE,qCAAqC;IAC3CY,GAAG,EAAE,CAACtE,IAAI,CAACyL,gCAAgC;GAC5C,EACD;IACEjI,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,iCAAiC;IACvCY,GAAG,EAAE,CAACtE,IAAI,CAAC0L,4BAA4B;GACxC,EACD;IACElI,IAAI,EAAE,wBAAwB;IAC9BE,IAAI,EAAE,gCAAgC;IACtCY,GAAG,EAAE,CAACtE,IAAI,CAAC2L,2BAA2B;GACvC,EACD;IACEnI,IAAI,EAAE,sBAAsB;IAC5BE,IAAI,EAAE,8BAA8B;IACpCmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,qDAAqD;IAC3DE,IAAI,EAAE,6CAA6C;IACnDmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,uCAAuC;IAC7CmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,4BAA4B;IAClCmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,2BAA2B;IACjCE,IAAI,EAAE,2BAA2B;IACjCmD,IAAI,EAAE;GACP,EACD;IACErD,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,oBAAoB;IAC1BY,GAAG,EAAE,CAACtE,IAAI,CAAC4L,4BAA4B;GACxC,EACD;IACEpI,IAAI,EAAE,0BAA0B;IAChCE,IAAI,EAAE,kCAAkC;IACxCY,GAAG,EAAE,CAACtE,IAAI,CAAC6L,4BAA4B,CAAC,CAAE;GAC3C;CAEJ,EACD;EACErI,IAAI,EAAE,UAAU;EAChB6C,IAAI,EAAE,mBAAmB;EACzBhD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,6BAA6B;IACnCE,IAAI,EAAE,sCAAsC;IAC5CY,GAAG,EAAE,CAACtE,IAAI,CAAC4K,gCAAgC;GAC5C,EACD;IACEpH,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,wCAAwC;IAC9CY,GAAG,EAAE,CAACtE,IAAI,CAAC6K,+BAA+B;GAC3C,EACD;IACErH,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,6BAA6B;IACnCY,GAAG,EAAE,CAACtE,IAAI,CAAC+K,qBAAqB;GACjC,EACD;IACEvH,IAAI,EAAE,gCAAgC;IACtCE,IAAI,EAAE,yCAAyC;IAC/CY,GAAG,EAAE,CAACtE,IAAI,CAAC8K,iCAAiC;GAC7C,EACD;IACEtH,IAAI,EAAE,wCAAwC;IAC9CE,IAAI,EAAE,+CAA+C;IACrDY,GAAG,EAAE,CAACtE,IAAI,CAACmL,0BAA0B;GACtC,EACD;IACE3H,IAAI,EAAE,sCAAsC;IAC5CE,IAAI,EAAE,6CAA6C;IACnDY,GAAG,EAAE,CAACtE,IAAI,CAACmL,0BAA0B;GACtC;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}